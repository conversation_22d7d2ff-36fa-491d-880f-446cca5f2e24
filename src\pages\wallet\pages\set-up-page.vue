<template>
  <div class="page-setup">
    <u-navbar title="钱包设置" :placeholder="true" :autoBack="true"> </u-navbar>

    <petro-layout ref="layout" :petroKeyboard="true">
      <zyzx-page-wallet-setup :list="list" />
    </petro-layout>
  </div>
</template>

<script>
export default {
  data() {
    return {
      list: [
        {
          title: '修改密码',
          arrow: true,
          link: '/pages/wallet/pages/face-recognition-page',
        },
        {
          title: '注销单位卡',
          arrow: true,
          link: '/pages/wallet/pages/wallet-logoff',
          routeType: 'redirectTo', // 添加这个字段
        }
      ],
    };
  },
  onLoad(query) {
    // const navigateType =query.data ? JSON.parse(query.data).navigateType : '';
    //const mainAccountNo = query.data ? JSON.parse(query.data).mainAccountNo : '';

  },
  onShow() { },
  created() { },
  mounted() { },
  methods: {}
};
</script>

<style scoped lang="scss">
.page-setup {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
</style>
