<template>
  <div class="page-verification">
    <view class="header">
      <image class="header-logo" src="@/static/icon-logo.png"></image>
      <!-- <view class="header-title">欢迎来到中油智行</view> -->
      <view class="header-value">
        <view class="header-title">欢迎来到中油智行</view>
        <view class="header-subTitle">智行在手&nbsp;便捷无忧</view>
      </view>
    </view>

    <view class="title">请输入短信验证码</view>
    <view class="subTitle">短信验证码发送至 {{ mobile }}</view>
    <view class="code">
      <u-code-input :adjustPosition="false" v-model="messageCode" :focus="codeFocus"></u-code-input>
    </view>

    <button @click="onInputFinish()">确认</button>

    <view class="time">
      <view v-show="isCountDown" class="time-countdown">
        <u-count-down ref="countDown" :time="time * 1000" format="ss" :autoStart="false" @finish="onFinish"> </u-count-down>
        秒后重新发送
      </view>
      <view v-show="!isCountDown" @click="sliderVisable = true">重新发送验证码</view>
      <view class="time-tips" @click="tipShow = true">没收到验证码？</view>
    </view>
    <u-modal :show="tipShow" confirmText="我知道了" @confirm="tipShow = false">
      <view class="slot-content">
        <view>收不到验证码</view>
        <view>1. 手机号码可能是为欠费停机状态，请及时充缴话费。</view>
        <view>2. 短信发送超过当天频次限制，国内短信24小时限制5次。</view>
        <view>3. 手机短信收件箱过满或手机内存不足，需清空后重启手机。</view>
        <view>4. 手机型号为双卡双待，请确认 AB 卡槽是否正确安放（可尝试更换 AB 卡位置重试）。</view>
        <view>5. 手机可能开启短信防骚扰功能。</view>
        <view>6. 如本人手机号码已停用，可通过账号找回更换手机号码。或联系您的客户服务网点，提供相关资质信息后更换手机号码。</view>
        <view>7. 如仍无法解决，请联系956100咨询客服。</view>
      </view>
    </u-modal>

    <!-- 滑块验证码弹窗 -->
    <div v-if="sliderVisable">
      <p-slider-verify
        :show="sliderVisable"
        :mobile="mobile"
        :messageType="messageType"
        :tempCode="tempCode"
        @success="onSliderVerifySuccess"
        @close="onSliderClose"
      />
    </div>
  </div>
</template>

<script>
import pSliderVerify from '@/components/p-slider-verify/p-slider-verify.vue';

export default {
  components: { pSliderVerify },
  data() {
    return {
      messageType: '1', // 1-用户注册 2-忘记密码 3-登录
      mobile: '', // 手机号
      tempCode: '', // 临时码 -- 登录界面传递
      location: {}, // 位置信息
      codeFocus: true, // 输入框是否聚焦
      messageCode: '', // 验证码
      messageCodeLength: 6, // 验证码长度
      time: 60, // 短信验证再次发送倒计时
      isCountDown: true, // 倒计时是否显示
      tipShow: false, // 短信验证提示
      isLoading: false, // 加载中
      sliderVisable: false, // 滑块验证弹窗
      saveInfo: {}, // 存储信息
      roleList: [], // 登录账号企业下角色列表
      role: {}, // 登录账号默认角色
    };
  },
  computed: {},
  onLoad(query) {
    console.log('verification', query);
    this.messageType = query.messageType;
    this.mobile = query.mobile;
    this.tempCode = query.tempCode;
    if (query?.locationInfo) {
      this.location = JSON.parse(decodeURIComponent(query.locationInfo));
    }
  },
  mounted() {
    this.$refs.countDown.start();
  },
  methods: {
    // 倒计时
    onFinish() {
      this.isCountDown = false;
      this.$refs.countDown.reset();
    },
    // 验证码输入完成
    async onInputFinish() {
      if (this.messageCode.length < 6) return uni.showToast({ content: '请输入6位验证码' });
      if (this.messageType == '1') {
        this.register();
      } else if (this.messageType == '2') {
        this.checkCode();
      } else {
        this.login();
      }
    },
    // 注册
    async register() {
      try {
        const res = await uni.$petro.http(
          'user.register.h5',
          {
            mobile: this.mobile,
            messageCode: this.messageCode,
          },
          { riskField: true },
        );
        console.log('res', res);

        this.turnToSetting('1', res.data.tempCode);
      } catch (e) {
        this.onReenter();
      }
    },
    // 校验验证码 -- 忘记密码
    async checkCode() {
      try {
        /**
         * mobile 手机号
         * type 验证码类型：（定义枚举类） 2-忘记密码（非登录态）； 3-提交入驻指导信息
         * messageCode 验证码
         */
        const res = await uni.$petro.http(
          'user.messageCode.check.h5',
          {
            mobile: this.mobile,
            messageCode: this.messageCode,
            type: '2',
          },
          { riskField: true },
        );
        console.log('res', res);
        this.turnToSetting('2', res.data.tempCode);
      } catch (e) {
        this.onReenter();
      }
    },
    // 登录
    async login() {
      /**
       * loginType 1—使用登录临时码及短信登录(app) 3—手机号+密码登录(app) 4—短信+换设备短信临时码(app) 5—换设备人脸临时码(app)
       * mobile 手机
       * tempCode 临时码 若loginType值为1、4、5，该字段必填。
       * messageCode 短信码 若loginType值为1、4，该字段必填。
       * password 登录密码。若loginType值为3，该字段必填。
       * loginLocation 登录地区
       * loginGps 登录gps坐标
       */
      try {
        const res = await uni.$petro.http(
          'user.login.h5',
          {
            loginType: '4',
            mobile: this.mobile,
            tempCode: this.tempCode,
            messageCode: this.messageCode,
            loginLocation: this.location?.address?.province + this.location?.address?.city,
            loginGps: this.location?.longitude ? `${this.location?.longitude},${this.location?.latitude}` : '',
          },
          { riskField: true },
        );
        console.log('res', res);

        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = res.data || {};
        this.getUserRole();
      } catch (e) {
        // 临时码已过期
        if (e.errorCode === 'B_B15_505777' || e.errorCode === 'B_B15_100001') {
          setTimeout(() => {
            uni.$petro.route({
              type: 'back',
              delta: 1, // 默认是1，表示返回上一页，可不传delta
            });
          }, 500);
          return;
        }
        this.onReenter();
      }
    },
    // 跳转设置密码
    turnToSetting(actionType, tempCode) {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/password-setting/password-setting',
          params: {
            actionType,
            tempCode,
          },
          type: 'redirectTo',
        });
      }, 500);
    },
    // 重新输入验证码
    onReenter() {
      this.messageCode = '';
      this.isLoading = false;
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisable = false;
      uni.showToast({ content: '短信验证码已发送' });
      this.isCountDown = true;
      this.$refs.countDown.start();
    },
    // 滑块验证关闭
    onSliderClose(data) {
      this.sliderVisable = false;
      if (data?.isExpired) {
        setTimeout(() => {
          uni.$petro.route({
            type: 'back',
            delta: 1, // 默认是1，表示返回上一页，可不传delta
          });
        }, 500);
      }
    },
    // 获取用户角色
    async getUserRole(reload = false) {
      try {
        const res = await uni.$petro.http('user.role.list.h5', {});
        console.log('res', res);

        // 获取角色列表--过滤掉企业管理员
        this.roleList = (res?.data || []).filter(item => item.code !== '1');

        // 判断是否有默认角色
        this.role = this.roleList.find(item => item.defaultFlag === 1) || {};

        if (reload) return;

        // 7-7正式供应商
        if (this.role?.code === '7-7') {
          setTimeout(() => {
            uni.$petro.switchTinyApp(this.role);
          }, 500);
          return;
        }

        await this.getUserEnterprise();
      } catch (e) {
        this.isLoading = false;
      }
    },
    // 获取获取已、未开通企业列表
    async getUserEnterprise() {
      try {
        const res = await uni.$petro.http('user.business.queryEnterpriseBusinessList.h5', {
          queryType: 5,
        });
        console.log('res', res);

        let managerList = (res?.data || []).filter(item => item.role === 2 && item.accountStatus !== 3); // 未注销企业列表
        let driverList = (res?.data || []).filter(item => item.role === 4 && item.accountStatus !== 3); // 未注销司机列表

        // 2-11礼品卡券管理员
        if (this.role?.code === '2-11') {
          let giftInfo = managerList.find(item => item.businessNo == this.role.businessNo && item.businessType === 11);
          if (giftInfo) {
            return [1, 2].includes(giftInfo.accountStatus) ? this.toGift() : this.toSetting();
          }
        }

        // 2-6非油直销业务联系人
        if (this.role?.code === '2-6') {
          let directsaleInfo = managerList.find(item => item.businessNo == this.role.businessNo && item.businessType === 6);
          if (directsaleInfo) {
            return [1, 2].includes(directsaleInfo.accountStatus) ? this.toDirectsale() : this.toSetting();
          }
        }

        let managerActivatedList = managerList.filter(item => [1, 2].includes(item.accountStatus) && item.businessType === 10); // 已激活/冻结车队业务联系人列表
        let driverActivatedList = driverList.filter(item => [1, 2].includes(item.accountStatus)); // 已激活/冻结司机列表

        // 已激活/冻结车队业务联系人
        let managerInfo = managerActivatedList.find(
          item => item.businessNo === this.role?.businessNo && item.enterpriseNo === this.saveInfo?.orgCode && this.role?.code === '2-10',
        );
        if (managerInfo) return this.toManager(managerInfo.businessNo);

        // 已激活/冻结司机
        let driverInfo = driverActivatedList.find(
          item => item.businessNo === this.role?.businessNo && item.enterpriseNo === this.saveInfo?.orgCode && this.role?.code === '4-10',
        );
        if (driverInfo) return this.toDriver(driverInfo.businessNo);

        if (managerActivatedList?.length) {
          // 已激活/冻结车队业务联系人--切换登录信息
          await this.switchCompanyAndRole(managerActivatedList[0].enterpriseNo, 2, 10, managerActivatedList[0].businessNo);
          this.toManager(managerActivatedList[0].businessNo);
        } else if (driverActivatedList?.length) {
          // 已激活/冻结司机--切换登录信息
          await this.switchCompanyAndRole(driverActivatedList[0].enterpriseNo, 4, 10, driverActivatedList[0].businessNo);
          this.toDriver(driverActivatedList[0].businessNo);
        } else if (managerList.length || driverList.length) {
          // 有待激活企业
          this.toSetting();
        } else {
          // 无待激活企业--跳转产品页
          this.toProduct();
        }
      } catch (e) {
        this.isLoading = false;
        this.toProduct();
      } finally {
        setTimeout(() => {
          this.isLoading = false;
        }, 800);
      }
    },
    // 切换企业和角色
    async switchCompanyAndRole(enterpriseNo, roleType, roleBusinessType, businessNo) {
      try {
        const res = await uni.$petro.http('user.companySwitch.h5', {
          orgCode: enterpriseNo,
          type: 1,
          roleType,
          roleBusinessType,
          businessNo,
        });
        console.log('switchCompanyAndRole', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = res.data;
      } catch (e) {
        console.log(e);
      }
    },
    // 获取车队业务类型
    async getFleetType(businessNo) {
      try {
        const res = await uni.$petro.http('user.business.getFleetType.h5', {
          businessNo,
        });
        console.log('getFleetType', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = Object.assign(this.saveInfo, res.data);
      } catch (e) {
        console.log(e);
      }
    },
    // 跳转管理端
    async toManager(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-10' });
      }, 500);
    },
    // 跳转司机端
    async toDriver(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '4-10' });
      }, 500);
    },
    // 跳转礼品卡
    toGift() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/gift-code/gift-code', type: 'reLaunch' });
      }, 500);
    },
    // 跳转非油直销
    toDirectsale() {
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-6' });
      }, 500);
    },
    // 跳转激活页面
    toSetting() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/todo/todo', params: { isInit: 1 }, type: 'redirectTo' });
      }, 500);
    },
    // 跳转业务选择页面
    toProduct() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/product/product', type: 'redirectTo' });
      }, 500);
    },
  },
};
</script>

<style scoped lang="scss">
.page-verification {
  background: #ffffff;
  padding: 0 0 36rpx;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  position: fixed;
}

.header {
  width: 100%;
  height: 480rpx;
  background: linear-gradient(53deg, rgba(121, 68, 255, 0.06) 0%, rgba(58, 145, 255, 0.15) 40%, rgba(41, 81, 154, 0.48) 100%);
  display: flex;
  align-items: flex-start;
  padding: 260rpx 32rpx 0;
  box-sizing: border-box;

  &-logo {
    width: 96rpx;
    height: 96rpx;
    margin-right: 32rpx;
  }

  &-value {
    flex: 1;
  }

  &-title {
    // flex: 1;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 62rpx;
  }

  &-subTitle {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 32rpx;
    margin-top: 4rpx;
  }
}

.title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #000000;
  line-height: 42rpx;
  margin-top: 24rpx;
  text-align: center;
}

.subTitle {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 32rpx;
  margin-top: 8rpx;
  text-align: center;
}

::v-deep .u-code-input__item {
  width: 96rpx !important;
  height: 96rpx !important;
  background: #f5f6f7 !important;
  border: none !important;
  border-radius: 16rpx !important;

  & > text {
    font-size: 60rpx !important;
    color: #333 !important;
  }
}

.code {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  margin: 40rpx 0 24rpx;
  padding: 0 32rpx;

  .box {
    width: 96rpx;
    height: 96rpx;
    background: #f5f6f7;
    border-radius: 16rpx;
    box-sizing: border-box;
    margin-right: 32rpx;
    line-height: 96rpx;
    font-size: 60rpx;
    color: #333;
    text-align: center;

    &-last {
      margin: 0;
    }

    .line {
      font-size: 56rpx;
      opacity: 0;
      animation-name: donghua;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
      animation-duration: 0.5s;
      animation-direction: alternate;
    }
    @keyframes donghua {
      0% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }
  }

  .input {
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    opacity: 0;
    padding: 0;
  }
}

.time {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 28rpx;
  display: flex;
  margin-left: 136rpx;

  &-tips {
    color: #fa1919;
    margin-left: 20rpx;
  }

  &-countdown {
    display: flex;

    ::v-deep .u-count-down__text {
      font-size: 24rpx;
      color: #999999;
      line-height: 28rpx;
    }
  }
}

button {
  height: 98rpx;
  background: #fa1919;
  border: none;
  border-radius: 49rpx;
  text-align: center;
  line-height: 98rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  margin: 48rpx 32rpx 64rpx;
  letter-spacing: 18rpx;
  text-indent: 18rpx;

  &:active {
    opacity: 0.8;
  }
}
</style>
