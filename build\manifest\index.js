const babel = require('@babel/core');
const fs = require('fs');
const Manifest = require('../../src/.manifest.json');
const configLocalPath = './src/config.index.local.js';

class buildManifest {
  constructor() {
    this.buildManifest();
  }

  async buildManifest() {
    const result = await babel.transformFileSync(configLocalPath, {
      presets: ['@babel/preset-env'],
    });
    const transformedCode = result.code;
    const configModule = new Function('module', 'exports', transformedCode);
    const exports = {};
    configModule(exports, exports);

    const config = exports.default;

    Manifest['mp-weixin'].appid = config.wx?.appid || '';
    Manifest['mp-weixin'].plugins = config.wx?.plugins || {};
    Manifest['mp-alipay'].plugins = process.env.UNI_SCRIPT === 'mp-mpaas' ? config.app?.plugins || {} : config.zfb?.plugins || {};

    const isSubpackage = process.argv.find(v => v.includes('--subpackage'));
    if (isSubpackage) {
      const subPath = isSubpackage.split('=').pop();
      if (subPath) {
        let configLocalStr = fs.readFileSync(configLocalPath, 'utf8');
        configLocalStr = configLocalStr.replace("subPath: ''", `subPath: '${subPath}'`);
        fs.writeFileSync(configLocalPath, configLocalStr);
      }
    }

    fs.writeFileSync('./src/manifest.json', JSON.stringify(Manifest, null, 2));
  }

  apply(compiler) {}
}

module.exports = buildManifest;
