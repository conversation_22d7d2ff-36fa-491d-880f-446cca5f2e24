import iconHome from '@/static/images/tab/home.png';
import iconHomeActive from '@/static/images/tab/home-active.png';
import iconWallet from '@/static/images/tab/wallet.png';
import iconWalletActive from '@/static/images/tab/wallet-active.png';
import iconOrder from '@/static/images/tab/order.png';
import iconOrderActive from '@/static/images/tab/order-active.png';
import iconMine from '@/static/images/tab/mine.png';
import iconMineActive from '@/static/images/tab/mine-active.png';

export default {
  state: {
    tabBarValue: 'gift-code',
    tabBarCache: ['gift-code'],
    tabBarList: [
      {
        name: '首页',
        value: 'gift-code',
        icon: iconHome,
        iconActive: iconHomeActive,
      },
      {
        name: '钱包',
        value: 'wallet',
        icon: iconWallet,
        iconActive: iconWalletActive,
      },
      {
        name: '订单',
        value: 'order',
        icon: iconOrder,
        iconActive: iconOrderActive,
      },
      {
        name: '我的',
        value: 'mine',
        icon: iconMine,
        iconActive: iconMineActive,
      },
    ],
  },
  mutations: {
    switchTab(state, payload) {
      state.tabBarValue = payload;
      if (!state.tabBarCache.includes(payload)) {
        state.tabBarCache.push(payload);
      }
    },
  },
  actions: {},
  getters: {},
};
