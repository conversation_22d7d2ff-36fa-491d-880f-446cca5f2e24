---
description: 企业小程序前端公共开发规则
globs: *.vue,*.js,*.scss,*.css,*.json
alwaysApply: true
---

# 前端公共开发规则

## 总体描述

该文档规定了企业小程序前端开发的公共规则，包括命名规范、代码风格、注释规范、工具配置等基础开发标准，确保代码的一致性、可读性和可维护性。

## 应用范围

本文档适用于企业小程序项目的所有前端开发人员，涵盖Vue组件、JavaScript代码、SCSS样式、配置文件等所有前端代码的编写规范。

## 使用要求

开发人员在进行前端开发时必须严格遵循本文档的所有规范要求，代码提交前必须通过ESLint检查，确保代码质量和团队协作效率。

## 规则1 命名规范

### 1.1 文件命名

**Vue组件文件**
- 页面组件：使用短横线命名法，如 `user-profile.vue`
- 业务组件：使用p-前缀 + 短横线命名，如 `p-home.vue`、`p-report.vue`
- 通用组件：使用短横线命名法，如 `data-list.vue`

```javascript
// ✅ 正确示例
components/
├── p-tabbar/p-tabbar.vue        // 业务组件
├── p-login-password/p-login-password.vue  // 业务组件
├── p-slider-verify/p-slider-verify.vue    // 业务组件
└── p-agreement/p-agreement.vue  // 业务组件

// ❌ 错误示例
components/
├── Home.vue                   // 缺少前缀和目录结构
├── loginComponent.vue         // 驼峰命名不规范
└── DataList.vue              // 缺少目录结构
```

**JavaScript文件**
- 工具函数：使用短横线命名，如 `date-utils.js`
- API接口：按模块命名，如 `api.js`、`account.js`
- 配置文件：使用点分隔，如 `config.global.js`

**SCSS文件**
- 全局样式：`global.scss`、`variables.scss`
- 组件样式：与组件同名，如 `p-tabbar.scss`

### 1.2 变量命名

**JavaScript变量**
- 使用驼峰命名法：`userName`、`isLoading`、`userList`
- 常量使用大写下划线：`API_BASE_URL`、`MAX_RETRY_COUNT`
- 私有变量使用下划线前缀：`_privateMethod`

```javascript
// ✅ 正确示例
const userName = 'admin';
const isLoading = false;
const userList = [];
const API_BASE_URL = 'https://api.example.com';

// ❌ 错误示例
const user_name = 'admin';        // 应使用驼峰命名
const IsLoading = false;          // 首字母不应大写
const USERLIST = [];              // 常量应有明确含义
```

**Vue组件内变量**
- data属性：驼峰命名法
- computed属性：驼峰命名法，体现计算性质
- methods方法：驼峰命名法，动词开头

```javascript
// ✅ 正确示例
export default {
  data() {
    return {
      userInfo: {},
      isVisible: false,
      currentPage: 1
    };
  },
  computed: {
    filteredList() {
      return this.userList.filter(item => item.active);
    },
    hasPermission() {
      return this.userInfo.role === 'admin';
    }
  },
  methods: {
    handleSubmit() {},
    fetchUserData() {},
    validateForm() {}
  }
};
```

### 1.3 CSS类名命名

**使用BEM命名法**
- 块（Block）：`.user-card`
- 元素（Element）：`.user-card__avatar`、`.user-card__name`
- 修饰符（Modifier）：`.user-card--active`、`.user-card__avatar--large`

```scss
// ✅ 正确示例
.p-tabbar {
  padding: 16rpx;

  &__icon {
    width: 50rpx;
    height: 50rpx;

    &--active {
      color: #FA1919;
    }
  }

  &__text {
    font-size: 24rpx;
    color: #767678;
    
    &--active {
      color: #FA1919;
    }
  }
}

// ❌ 错误示例
.tabbar {              // 缺少p-前缀
  .icon {              // 缺少BEM结构
    .active {          // 嵌套过深
      color: #FA1919;
    }
  }
}
```

## 规则2 Vue组件开发规范

### 2.1 组件结构

**标准组件结构顺序**

```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
// 导入依赖
import { mapState, mapActions } from 'vuex';

export default {
  name: 'ComponentName',
  components: {},
  props: {},
  data() {
    return {};
  },
  computed: {
    ...mapState(['userInfo']),
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    ...mapActions(['fetchData']),
  },
};
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

### 2.2 Props定义规范

**必须包含类型、默认值和验证**

```javascript
// ✅ 正确示例
props: {
  title: {
    type: String,
    required: true,
    validator: value => value.length > 0
  },
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  },
  userList: {
    type: Array,
    default: () => []
  },
  config: {
    type: Object,
    default: () => ({})
  }
}

// ❌ 错误示例
props: ['title', 'size', 'userList']  // 缺少类型定义
```

### 2.3 事件命名规范

**使用动词-名词格式，采用短横线命名**

```javascript
// ✅ 正确示例
this.$emit('update-user', userData);
this.$emit('delete-item', itemId);
this.$emit('toggle-visibility', isVisible);

// ❌ 错误示例
this.$emit('updateUser', userData);     // 应使用短横线
this.$emit('delete', itemId);           // 缺少具体对象
this.$emit('toggle', isVisible);        // 缺少具体对象
```

## 规则3 JavaScript代码规范

### 3.1 ES6+语法使用

**必须使用现代JavaScript语法**

```javascript
// ✅ 正确示例 - 使用箭头函数
const fetchUserData = async (userId) => {
  try {
    const { success, data } = await uni.$petro.http('user.getInfo.h5', { userId });
    return success ? data : null;
  } catch (error) {
    console.error('获取用户数据失败:', error);
    return null;
  }
};

// ✅ 正确示例 - 使用解构赋值
const { userInfo, isLoading } = this;
const { name, age, email } = userInfo;

// ✅ 正确示例 - 使用扩展运算符
const newUser = { ...userInfo, lastLoginTime: Date.now() };
const mergedList = [...list1, ...list2];

// ❌ 错误示例 - 使用过时语法
var self = this;                        // 应使用箭头函数
var userName = userInfo.name;           // 应使用解构赋值
var newList = list1.concat(list2);     // 应使用扩展运算符
```

### 3.2 异步处理规范

**统一使用async/await，避免回调地狱**

```javascript
// ✅ 正确示例
async methods: {
  async handleSubmit() {
    try {
      this.isLoading = true;

      // 并行请求
      const [userResult, configResult] = await Promise.all([
        this.fetchUserInfo(),
        this.fetchConfig()
      ]);

      if (userResult.success && configResult.success) {
        this.processData(userResult.data, configResult.data);
      }
    } catch (error) {
      this.handleError(error);
    } finally {
      this.isLoading = false;
    }
  },

  async fetchUserInfo() {
    return await uni.$petro.http('user.getInfo.h5', {
      userId: this.userId
    });
  }
}

// ❌ 错误示例 - 回调地狱
methods: {
  handleSubmit() {
    uni.$petro.http('user.getInfo.h5', { userId: this.userId }, {
      success: (res) => {
        uni.$petro.http('config.get.h5', {}, {
          success: (configRes) => {
            // 嵌套过深
          },
          fail: (err) => {
            console.error(err);
          }
        });
      },
      fail: (err) => {
        console.error(err);
      }
    });
  }
}
```

### 3.3 错误处理规范

**统一的错误处理机制**

```javascript
// ✅ 正确示例
async methods: {
  async fetchData() {
    try {
      const { success, data, message } = await uni.$petro.http('api.getData.h5', {});

      if (!success) {
        throw new Error(message || '请求失败');
      }

      this.dataList = data;
    } catch (error) {
      console.error('获取数据失败:', error);
      uni.showToast({
        title: error.message || '网络错误，请重试',
        icon: 'none'
      });
    }
  },

  handleError(error) {
    // 统一错误处理逻辑
    const errorMessage = this.getErrorMessage(error);
    uni.showToast({
      title: errorMessage,
      icon: 'none'
    });
  },

  getErrorMessage(error) {
    if (error.code === 401) return '登录已过期，请重新登录';
    if (error.code === 403) return '权限不足';
    if (error.code === 500) return '服务器错误，请稍后重试';
    return error.message || '操作失败，请重试';
  }
}
```

## 规则4 SCSS样式规范

### 4.1 样式组织结构

**使用嵌套和BEM命名法**

```scss
// ✅ 正确示例
.p-login-password {
  padding: 32rpx;
  background: #fff;

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: 24rpx;

    &--compact {
      margin-bottom: 16rpx;
    }
  }

  &__title {
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 8rpx;
  }

  &__subtitle {
    font-size: 28rpx;
    color: #666;
  }

  &__form {
    margin-top: 32rpx;
  }

  &__input {
    margin-bottom: 24rpx;
  }

  &__button {
    margin-top: 40rpx;
  }

  &--error {
    border: 2rpx solid #FA1919;
  }
}

// ❌ 错误示例
.loginPassword {                  // 应使用短横线命名
  .header {                       // 缺少BEM结构
    .title {                      // 嵌套过深
      font-size: 36rpx;
    }
  }
}
```

### 4.2 响应式单位使用

**统一使用rpx单位，特殊情况使用px**

```scss
// ✅ 正确示例
.container {
  padding: 32rpx;              // 使用rpx适配不同屏幕
  font-size: 28rpx;            // 字体大小使用rpx
  border-width: 1px;           // 边框使用px保证清晰
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // 阴影使用px
}

// ❌ 错误示例
.container {
  padding: 16px;               // 应使用rpx
  font-size: 14px;             // 应使用rpx
  border-width: 1rpx;          // 边框应使用px
}
```

### 4.3 颜色和变量使用

**使用SCSS变量统一管理颜色**

```scss
// uni.scss
$uni-color-primary: #007aff;
$uni-color-success: #4cd964;
$uni-color-warning: #ff9500;
$uni-color-error: #ff3b30;

$uni-text-color: #333;
$uni-text-color-grey: #999;

$uni-bg-color: #fff;
$uni-bg-color-grey: #f8f8f8;

$petro-red: #FA1919;
$petro-orange: #FF7033;

// ✅ 正确示例 - 使用变量
.button {
  background-color: $uni-color-primary;
  color: #fff;
  border: 1px solid $uni-color-primary;

  &--success {
    background-color: $uni-color-success;
    border-color: $uni-color-success;
  }

  &--disabled {
    background-color: $uni-bg-color-grey;
    color: $uni-text-color-grey;
    border-color: $uni-bg-color-grey;
  }
}

// ❌ 错误示例 - 硬编码颜色
.button {
  background-color: #007aff;      // 应使用变量
  color: #ffffff;                 // 应使用变量
}
```

## 规则5 注释规范

### 5.1 JavaScript注释

**使用JSDoc格式注释函数和复杂逻辑**

```javascript
/**
 * 获取用户信息
 * @description 根据用户ID获取用户详细信息，包括基本信息和权限信息
 * @param {string} userId - 用户ID
 * @param {Object} options - 可选参数
 * @param {boolean} options.includePermissions - 是否包含权限信息
 * @returns {Promise<Object>} 返回用户信息对象
 * @example
 * const userInfo = await getUserInfo('123', { includePermissions: true });
 */
async function getUserInfo(userId, options = {}) {
  // 参数验证
  if (!userId) {
    throw new Error('用户ID不能为空');
  }

  try {
    // 发起API请求
    const { success, data } = await uni.$petro.http('user.getInfo.h5', {
      userId,
      ...options
    });

    return success ? data : null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    throw error;
  }
}

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式化模板，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
function formatDate(date, format = 'YYYY-MM-DD') {
  // 实现逻辑...
}
```

### 5.2 Vue组件注释

**组件必须包含完整的注释说明**

```vue
<template>
  <!-- 登录密码组件 -->
  <view class="p-login-password">
    <!-- 头部区域 -->
    <view class="p-login-password__header">
      <text class="p-login-password__title">{{ title }}</text>
      <text class="p-login-password__subtitle">{{ subtitle }}</text>
    </view>

    <!-- 表单区域 -->
    <view class="p-login-password__form">
      <!-- 手机号输入框 -->
      <u-input
        v-model="mobile"
        placeholder="请输入手机号"
        class="p-login-password__input"
      />
      
      <!-- 密码输入框 -->
      <u-input
        v-model="password"
        type="password"
        placeholder="请输入密码"
        class="p-login-password__input"
      />
      
      <!-- 登录按钮 -->
      <u-button
        type="primary"
        :loading="loading"
        class="p-login-password__button"
        @click="handleLogin"
      >
        登录
      </u-button>
    </view>
  </view>
</template>

<script>
/**
 * 登录密码组件
 * @description 用户密码登录表单组件，包含手机号和密码输入
 * <AUTHOR>
 * @date 2024-01-01
 * @version 1.0.0
 */
export default {
  name: 'PLoginPassword',

  /**
   * 组件属性
   */
  props: {
    // 标题
    title: {
      type: String,
      default: '密码登录'
    },
    // 副标题
    subtitle: {
      type: String,
      default: '请输入手机号和密码'
    }
  },

  /**
   * 组件数据
   */
  data() {
    return {
      mobile: '',
      password: '',
      loading: false
    };
  },

  /**
   * 组件事件
   * @event success - 登录成功时触发，参数为用户信息
   * @event error - 登录失败时触发，参数为错误信息
   */

  methods: {
    /**
     * 处理登录请求
     */
    async handleLogin() {
      // 参数验证
      if (!this.mobile || !this.password) {
        uni.showToast({
          title: '请输入手机号和密码',
          icon: 'none'
        });
        return;
      }

      try {
        this.loading = true;
        
        // 发起登录请求
        const result = await this.loginRequest();
        
        // 触发成功事件
        this.$emit('success', result);
      } catch (error) {
        // 触发错误事件
        this.$emit('error', error.message);
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 发送登录请求
     * @returns {Promise<Object>} 登录结果
     */
    async loginRequest() {
      // 实现登录逻辑
    }
  }
};
</script>
```

### 5.3 SCSS注释

**样式注释说明设计意图和特殊处理**

```scss
/**
 * 登录密码组件样式
 * @description 登录表单的样式定义
 * <AUTHOR>
 * @date 2024-01-01
 */

.p-login-password {
  /* 基础布局 - 使用flex布局垂直排列 */
  display: flex;
  flex-direction: column;
  padding: 32rpx;
  background: #fff;

  /* 头部区域 - 标题和副标题 */
  &__header {
    margin-bottom: 40rpx;
  }

  &__title {
    /* 主标题样式 - 大号字体突出显示 */
    font-size: 36rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 12rpx;
  }

  &__subtitle {
    /* 副标题样式 - 较小字体和浅色 */
    font-size: 28rpx;
    color: #666;
  }

  /* 表单区域 - 输入框和按钮 */
  &__form {
    margin-top: 32rpx;
  }

  &__input {
    /* 输入框样式 - 底部边框和间距 */
    margin-bottom: 24rpx;
    border-bottom: 1px solid #e5e5e5;
    
    /* 输入框聚焦状态 */
    &:focus {
      border-color: $uni-color-primary;
    }
  }

  &__button {
    /* 按钮样式 - 上方间距和宽度 */
    margin-top: 40rpx;
    width: 100%;
  }
}
```

## 规则6 工具配置

### 6.1 ESLint配置

**项目必须配置ESLint进行代码质量检查**

```javascript
// .eslintrc.js
module.exports = {
  env: {
    browser: true,
    es6: true,
    node: true
  },
  extends: [
    'eslint:recommended',
    '@vue/standard'
  ],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module'
  },
  rules: {
    // 强制使用分号
    'semi': ['error', 'always'],
    // 强制使用单引号
    'quotes': ['error', 'single'],
    // 禁止未使用的变量
    'no-unused-vars': 'error',
    // 禁止console（生产环境）
    'no-console': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
    // Vue特定规则
    'vue/html-self-closing': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/singleline-html-element-content-newline': 'off'
  },
  globals: {
    uni: 'readonly',
    wx: 'readonly',
    getCurrentPages: 'readonly',
    getApp: 'readonly'
  }
};
```

### 6.2 Prettier配置

**统一代码格式化规则**

```javascript
// .prettierrc.js
module.exports = {
  // 使用单引号
  singleQuote: true,
  // 使用分号
  semi: true,
  // 行宽120字符
  printWidth: 120,
  // 使用2个空格缩进
  tabWidth: 2,
  // 不使用tab
  useTabs: false,
  // 尾随逗号
  trailingComma: 'es5',
  // 对象花括号内空格
  bracketSpacing: true,
  // JSX标签闭合位置
  jsxBracketSameLine: false,
  // 箭头函数参数括号
  arrowParens: 'avoid',
  // 换行符
  endOfLine: 'lf'
};
```

### 6.3 Git提交规范

**使用约定式提交格式**

```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat:     新功能
fix:      修复bug
docs:     文档更新
style:    代码格式调整（不影响功能）
refactor: 重构代码
test:     测试相关
chore:    构建过程或辅助工具的变动

# 示例
feat(user): 添加用户信息编辑功能
fix(login): 修复登录状态异常问题
docs(api): 更新API接口文档
style(home): 调整首页布局样式
refactor(utils): 重构日期工具函数
```

## 规则7 项目特定规范

### 7.1 uni.$petro工具使用规范

**项目封装的全局工具方法使用规范**

```javascript
// 路由跳转
uni.$petro.route({
  url: '/pages/detail/detail',
  type: 'navigateTo',
  params: {
    id: '123',
    from: 'list'
  }
});

// HTTP请求
const { success, data } = await uni.$petro.http('user.getInfo.h5', {
  userId: '123'
}, {
  showLoading: true
});

// 获取Token信息
const tokenInfo = await uni.$petro.getTokenInfo();

// 设置Token信息
await uni.$petro.setTokenInfo({
  accessToken: 'xxx',
  refreshToken: 'yyy'
});

// 切换小程序
uni.$petro.switchTinyApp({ code: '2-10' });

// 工具方法
const splitterName = uni.$petro.Utils.splitterName('张三');
```

### 7.2 条件编译规范

**处理不同平台差异的条件编译规范**

```javascript
// JavaScript中的条件编译
// #ifdef MP-WEIXIN
// 微信小程序特有代码
wx.getSystemInfo({
  success: (res) => {
    console.log(res);
  }
});
// #endif

// #ifdef MP-ALIPAY
// 支付宝小程序特有代码
my.getSystemInfo({
  success: (res) => {
    console.log(res);
  }
});
// #endif

// #ifdef H5
// H5特有代码
document.title = '页面标题';
// #endif

// Vue模板中的条件编译
<template>
  <view>
    <!-- #ifdef MP-WEIXIN -->
    <view>微信小程序特有UI</view>
    <!-- #endif -->
    
    <!-- #ifdef MP-ALIPAY -->
    <view>支付宝小程序特有UI</view>
    <!-- #endif -->
    
    <!-- #ifdef H5 -->
    <view>H5特有UI</view>
    <!-- #endif -->
    
    <!-- 所有平台通用UI -->
    <view>通用内容</view>
  </view>
</template>

// 样式中的条件编译
<style>
/* #ifdef MP-WEIXIN */
.wx-style {
  color: #007aff;
}
/* #endif */

/* #ifdef MP-ALIPAY */
.ali-style {
  color: #1677ff;
}
/* #endif */

/* 通用样式 */
.common-style {
  font-size: 28rpx;
}
</style>
```

## 总结

本规范文档涵盖了企业小程序前端开发的核心规范，包括命名规范、Vue组件开发、JavaScript代码规范、SCSS样式规范、注释规范和工具配置。开发人员必须严格遵循这些规范，确保代码质量和团队协作效率。

**重要提醒：**
1. 所有代码提交前必须通过ESLint检查
2. 组件开发必须包含完整的Props定义和事件说明
3. 异步操作统一使用async/await语法
4. 样式开发必须使用BEM命名法和SCSS变量
5. 复杂逻辑必须添加详细注释说明
6. 业务组件必须使用p-前缀并放置在独立目录中
7. 统一使用uni.$petro封装的工具方法