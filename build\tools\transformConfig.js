const babel = require('@babel/core');
const configLocalPath = './src/config.index.local.js';

class transformConfig {
  config = {};

  constructor() {
    this.transform();
  }

  async transform() {
    const result = await babel.transformFileSync(configLocalPath, {
      presets: ['@babel/preset-env'],
    });
    const transformedCode = result.code;
    const configModule = new Function('module', 'exports', transformedCode);
    const exports = {};
    configModule(exports, exports);

    this.config = exports.default;
    return this.config;
  }
}

module.exports = new transformConfig();
