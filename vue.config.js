const path = require('path');
const buildManifest = require('./build/manifest');
const tools = require('./build/tools');

module.exports = {
  publicPath: '/',
  productionSourceMap: false,
  configureWebpack: {
    resolve: {
      alias: {},
      fallback: {
        fs: false,
        crypto: false,
        path: false,
        ws: false,
      },
    },
    plugins: [new buildManifest()],
  },
  transpileDependencies: ['uview-ui', '@petro-soti/foudation-common', '@petro-soti/foudation-zyzx'],
  chainWebpack: config => {
    const configProject = tools.transformConfig.config;

    config.plugin('define').tap(args => {
      const isSubpackage = process.argv.find(v => v.includes('--subpackage'));
      if (isSubpackage) {
        const subPath = isSubpackage.split('=').pop();
        if (subPath) {
          const VUE_GWCLI_PATH = 'static/gwcli1-0-2.wasm.br' || 'utils/wasm/gwcli1-0-2.wasm.br';
          console.log('\n启用分包模式 subPath', subPath);
          if (args[0]['process.env'].VUE_APP_PLATFORM === '"mp-weixin"') {
            args[0]['process.env'].VUE_GWCLI_PATH = `"${VUE_GWCLI_PATH}"`;
          }
        }
      }
      return args;
    });

    if (configProject?.staticPath) {
      console.log('\n启用oss地址', configProject?.staticPath);
      const ossIncludeDir = path.resolve(__dirname, 'src');
      config.module
        .rule('oss-replace-loader')
        .test(/\.css$|\.scss$|\.js$|\.vue$|\.ts$/)
        .resource(_path => {
          return _path.includes(ossIncludeDir);
        })
        .use('string-replace-loader')
        .loader('string-replace-loader')
        .tap(_ => {
          return {
            search: '/static/',
            replace: (match, p1, offset, string) => {
              // console.log('oss-replace-loader', match, p1);
              return `${configProject.staticPath}`;
            },
            flags: 'g',
          };
        });
    }

    const includeDir = path.resolve(__dirname, 'node_modules');
    const excludeDir = path.resolve(__dirname, 'node_modules/@petro-soti/foudation-common');
    const excludeZYZXDir = path.resolve(__dirname, 'node_modules/@petro-soti/foudation-zyzx');
    config.module
      .rule('platform-replace-loader')
      // .test(/\.css$|\.scss$|\.js$|\.vue$|\.ts$/)
      // .exclude(/node_modules/)
      // .include(includeDir)
      .enforce('pre')
      .resource(_path => {
        if (_path.includes(excludeDir) || _path.includes(excludeZYZXDir)) {
          return false;
        }
        return _path.includes(includeDir);
      })
      .use('string-replace-loader')
      .loader('string-replace-loader')
      .tap(_ => {
        return {
          // 暂时固定写死，可做自定义平台替换优化
          search: 'MP-ALIPAY',
          replace: (match, p1, offset, string) => {
            // console.log('platform-replace-loader', match, p1);
            return `MP-ALIPAY || MP-MPAAS`;
          },
          flags: 'g',
        };
      });

    config.optimization.minimizer('terser').tap(args => {
      const compress = args[0].terserOptions.compress;
      compress.drop_console = true;
      compress.pure_funcs = ['__f__'];
      return args;
    });
  },
};
