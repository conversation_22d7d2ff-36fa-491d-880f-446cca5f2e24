<template>
  <div>
    <div>p-test components</div>
    <div>$store.state.count：{{ test1Count }} {{ test2Count }}</div>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex';

export default {
  name: 'p-test',
  props: {
    data: String,
  },
  data() {
    return {};
  },
  mounted() {},
  computed: {
    ...mapState({
      test1Count: state => state.test1.count,
    }),
    test2Count() {
      return this.$store.state.test2.count;
    },
  },
};
</script>

<style scoped lang="scss"></style>
