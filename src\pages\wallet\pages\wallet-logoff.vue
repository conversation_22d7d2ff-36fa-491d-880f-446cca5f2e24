<template>
  <div class="page-flex page-notices">
    <div class="container">
      <div class="scroll-view">
        <petro-scroll-view ref="scrollView" style="height: 100%" :scroll-y="true" :refresher-enabled="true"
          @refresherrefresh="onRefresh" :scrolltolower-enabled="false" :scrolltolower-bar-enabled="false"
          @scrolltolower="scrolltolower" :scroll-top-bar-enable="false" :safe-bottom="false">
          <div class="header">
            <u-navbar :title="' '" :autoBack="false" :border="false" :leftIconColor="'transparent'" :placeholder="true"
              :bgColor="'transparent'"></u-navbar>
          </div>
          <div class="main">
            <div class="top">
              <div class="title">将注销单位卡</div>
              <div class="wallet-info">
                <u-avatar :text="'昆'" size="80rpx" fontSize="36rpx" bg-color="#FF7033"></u-avatar>
                <div class="info">
                  <div class="name">
                    <span>{{ walletInfo.enterpriseName }}</span>
                    <image class="icon" src="/static/frame2.png"></image>
                  </div>
                  <div class="content">
                    <span>礼品卡券管理员：{{ walletInfo.userName }}</span>
                    <span>开户地：{{ walletInfo.accountPlaceName }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="cd">
              <div class="title">注销单位卡需要满足如下条件：</div>
              <div class="li">
                <image class="icon" src="/static/images/icon-wallet-logoff-01.png"></image>
                <div class="desc">
                  余额为
                  <span>0</span>
                  且所有交易已完结、无纠纷
                </div>
              </div>
              <div class="li">
                <image class="icon" src="/static/images/icon-wallet-logoff-02.png"></image>
                <div class="desc">如您存在未派发完成的售卡订单则不可注销</div>
              </div>
              <div class="li">
                <image class="icon" src="/static/images/icon-wallet-logoff-03.png"></image>
                <div class="desc">如您存在福利卡订单则不可注销</div>
              </div>
            </div>
            <div class="cd">
              <div class="title">
                如你满足条件并选择注销，注销后对应的业务将一并关闭，该业务下所有信息将被清空无法找回。本次注销不影响你其他业务的正常进行。
              </div>
              <div class="li">
                <image class="icon" src="/static/images/icon-wallet-logoff-04.png"></image>
                <div class="desc">该业务下单位及个人资料</div>
              </div>
              <div class="li">
                <image class="icon" src="/static/images/icon-wallet-logoff-05.png"></image>
                <div class="desc">该业务下的交易记录、发票记录</div>
              </div>
              <!-- <div class="li">
                <image class="icon" src="/static/images/icon-wallet-logoff-06.png"></image>
                <div class="desc">该业务下的积分、优惠金、优惠折扣等权益</div>
              </div> -->
            </div>
          </div>
        </petro-scroll-view>
      </div>

      <div class="footer">
        <div class="action">
          <button class="btn btn-cancel" @click="onCancel">放弃注销</button>
          <button class="btn btn-logoff" @click="onVerify">继续注销</button>
        </div>
        <u-safe-bottom></u-safe-bottom>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'page-notices',
  components: {},
  data() {
    return {};
  },
  async mounted() { },
  onShow() { },
  computed: {
    ...mapState({
      walletInfo: state => state.account.walletInfo,
    }),
  },
  methods: {
    scroll(e) { },
    onPulling() {
      console.log('onPulling');
    },
    onAbout() {
      console.log('onAbout');
    },
    onRefresh() {
      console.log('onRefresh');
      if (this._freshing) return;
      this._freshing = true;
      setTimeout(() => {
        this._freshing = false;
        this.getList(true);
        this.$refs.scrollView.stopRefresh();
      }, 500);
    },
    scrolltolower() {
      console.log('scrolltolower');
      this.getList();
    },
    getList(refresh) { },
    onCancel() {
      uni.navigateBack();
    },
    onVerify() {
      uni.showModal({
        title: '注销确认',
        content: '注销后会清空该业务下所有的信息和数据，是否确认注销？',
        confirmText: '确认注销',
        cancelText: '暂不注销',
        success: async res => {
          if (res.confirm) {
            try {
              uni.$petro.route(
                {
                  url: '/pages/wallet/pages/wallet-logoff-verification-page',
                  type: 'redirectTo',
                  params: {},
                },
                true,
              );
            } catch (error) {
              console.log(error);
            }
          } else if (res.cancel) {
            console.log('用户点击取消');
          }
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-notices {
  height: 100vh;
  background-color: #f8f8f8;

  >.container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .header {
    background-color: #fff;
  }

  .scroll-view {
    flex: 1;
    height: inherit;
    overflow: auto;
  }

  .main {
    padding: 0 0 48rpx;
  }

  .top {
    font-size: 48rpx;
    color: #333;
    background-color: #fff;
    padding: 52rpx 32rpx 48rpx 32rpx;

    .title {
      text-align: center;
      font-weight: bold;
    }

    .wallet-info {
      margin-top: 48rpx;
      background-color: #f7f9ff;
      border-radius: 16rpx;
      padding: 32rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .info {
        flex: 1;
        margin-left: 24rpx;
      }

      .name {
        font-size: 32rpx;
        color: #333;
        display: flex;
        align-items: center;

        >span {
          &:first-child {
            margin-right: 8rpx;
          }
        }

        image {
          width: 32rpx;
          height: 28rpx;
        }
      }

      .content {
        font-size: 24rpx;
        color: #666;

        >span {
          &:first-child {
            margin-right: 24rpx;
          }
        }
      }
    }
  }

  .cd {
    padding: 48rpx 32rpx calc(58rpx - 24rpx);
    margin: 24rpx 32rpx 0;
    background-color: #fff;
    border-radius: 16rpx;

    .title {
      font-size: 32rpx;
      color: #333;
      font-weight: bold;
    }

    .li {
      font-size: 28rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 24rpx;

      .icon {
        width: 80rpx;
        height: 80rpx;
        margin-right: 20rpx;
      }

      .desc {
        flex: 1;
      }
    }
  }

  .footer {
    padding: 28rpx 48rpx;
    background-color: #fff;

    .action {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn {
        padding: 20rpx 88rpx;
        font-size: 28rpx;
        color: #fa1919;
        background: #fadcdc;
        border-radius: 200rpx;

        display: flex;
        justify-content: center;
        align-items: center;

        &:last-child {
          color: #fff;
          background-color: #fa1919;
        }

        &:active {
          opacity: 0.8;
        }
      }
    }
  }
}
</style>
