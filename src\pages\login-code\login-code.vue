<template>
  <div class="page-login-code">
    <view class="container">
      <image class="logo" mode="widthFix" src="https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/login/logo.png"></image>

      <u--input v-model="phone" type="text" placeholder="请输入手机号" border="surround" shape="circle"></u--input>
      <view class="agree" @click="isAgree = !isAgree">
        <image class="agree-checkbox" :src="`/static/icon-${isAgree ? 'agree' : 'disagree'}.png`"></image>
        <view class="agree-content"> 我已阅读并同意<span>《服务协议》</span>和<span>《隐私政策》</span> </view>
      </view>
      <button @click="onGetCode()">获取验证码</button>
      <view class="methods" @click="onLoginClick()">本机号码一键登录</view>
      <view class="methods" @click="onLoginPassword()">账号密码登录</view>

      <view class="footer"> <view @click="onRegister()">注册</view>|<view @click="onMakePhone()">帮助</view> </view>
    </view>

    <!-- 滑块验证码弹窗 -->
    <p-slider-verify
      :show="sliderVisable"
      :sliderHint="sliderHint"
      @success="onSliderVerifySuccess"
      @close="sliderVisable = false"
      :initIsLoading="isLoading"
      :initSlideUrl="slideUrl"
      :initSlideBlockUrl="slideBlockUrl"
    />
  </div>
</template>

<script>
import pSliderVerify from '@/components/p-slider-verify/p-slider-verify.vue';

export default {
  components: { pSliderVerify },
  data() {
    return {
      phone: '', // 短信验证码登录手机号码
      isAgree: false, // 是否同意协议
      // 滑块验证码参数
      sliderVisable: false, // 滑块验证是否显示
      sliderHint: '请按住滑块，拖动到最右边',
      isLoading: false,
      slideUrl: null,
      slideBlockUrl: null,
    };
  },
  computed: {},
  onLoad(query) {
    console.log('login onLoad', query);
    try {
      if (query.data) query.data = JSON.parse(query.data);
      console.log('parse query.data', query.data);
    } catch (err) {
      console.error(err);
    }
  },
  methods: {
    // 获取验证码
    onGetCode() {
      if (!this.isAgree) return uni.showToast({ content: '请同意协议' });
      this.sliderVisable = true;
    },
    // 一键登录
    onLoginClick() {
      uni.$petro.route({ url: '/pages/login-click/login-click', type: 'redirectTo' });
    },
    // 注册
    onRegister() {
      uni.$petro.route({ url: '/pages/register/register', type: 'redirectTo' });
    },
    // 帮助 -- 跳转客服电话956100
    onMakePhone() {
      uni.makePhoneCall({
        phoneNumber: '956100',
      });
    },
    // 账号密码登录
    onLoginPassword() {
      uni.$petro.route({ url: '/pages/login-password/login-password', type: 'redirectTo' });
    },
    // 初始化滑块验证码
    initSlider(result) {
      // 滑块验证码自适应
      let windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
      console.log('document.body.clientWidth', windowWidth);

      let verifyImgWidth = parseInt(windowWidth * 0.88 - 30) + '';
      console.log('verifyImgWidth', verifyImgWidth, verifyImgWidth.length);

      verifyImgWidth = verifyImgWidth.substring(0, verifyImgWidth.length - 1) * 10;
      console.log('>>>>>>>verifyImgWidth', verifyImgWidth);

      this.verifyImgWidth = verifyImgWidth;
      this.verifyImgHeight = (verifyImgWidth * 155) / 300;

      // 初始化滑块验证码
      if (!result || result.code != 0 || !result.data) {
        this.isLoading = true;
        this.$toast(result.msg);
        return;
      }

      this.isLoading = !result.data.originalImageBase64;
      this.slideUrl = 'data:image/png;base64,' + (result.data.originalImageBase64 || '');
      this.slideBlockUrl = 'data:image/png;base64,' + (result.data.jigsawImageBase64 || '');
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisable = false;
      uni.showToast({ content: '短信验证码已发送' });
      uni.$petro.route({ url: '/pages/verification/verification', type: 'navigateTo' });
    },
  },
};
</script>

<style scoped lang="scss">
.page-login-code {
  background: #f5f5f5;
  padding: 180rpx 0 36rpx;
  text-align: center;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
}

.cover {
  width: 100%;
}

.logo {
  width: 100px;
  margin: 12px auto;
  display: inherit;
}

.container {
  padding: 10px;
}

.agree {
  display: flex;
  text-align: left;
  margin-top: 60rpx;

  &-checkbox {
    width: 32rpx;
    height: 32rpx;
    margin-top: 4rpx;
    margin-right: 6rpx;
  }

  &-content {
    flex: 1;
    font-size: 24rpx;
    color: #999999;
    line-height: 33rpx;

    & > span {
      color: #e64f22;
    }
  }
}

button {
  height: 88rpx;
  line-height: 88rpx;
  background: #e64f22;
  border-radius: 16rpx;
  margin-top: 20rpx;
  font-size: 30rpx;
  color: #ffffff;
}

.methods {
  margin-top: 22rpx;
}

.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 22rpx;
}

.verify-popup {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &-content {
    width: 90%;
    padding: 32rpx;
    box-sizing: border-box;
    background: #fff;
    border-radius: 16rpx;
  }
}
</style>
