---
description: 企业小程序前端API接口规范
globs: *.js,*.vue
alwaysApply: true
---

# 前端API接口规范

## 总体描述

本文档规定了企业小程序前端项目的API接口规范，包括接口封装、Mock数据、错误处理和请求配置等内容，确保项目API调用的一致性、可维护性和可扩展性。

## 应用范围

本文档适用于企业小程序项目的所有前端开发人员，涵盖API接口的定义、调用、错误处理和Mock数据管理等相关开发工作。

## 使用要求

开发人员在进行API接口相关开发时必须严格遵循本文档的规范要求，确保API调用的规范性和一致性。

## 规则1 接口封装规范

### 1.1 统一使用uni.$petro.http方法

**所有API请求必须使用uni.$petro.http方法**

```javascript
// ✅ 正确示例
// 基本用法
const { success, data, message } = await uni.$petro.http('user.getInfo.h5', {
  userId: '123'
});

// 带配置的用法
const { success, data, message } = await uni.$petro.http('user.getInfo.h5', {
  userId: '123'
}, {
  showLoading: true,
  loadingText: '加载中...',
  showError: true,
  errorTitle: '获取失败'
});

// ❌ 错误示例
// 不要直接使用uni.request
uni.request({
  url: 'https://api.example.com/user/getInfo',
  method: 'POST',
  data: { userId: '123' },
  success: (res) => {
    // 处理响应
  },
  fail: (err) => {
    // 处理错误
  }
});
```

### 1.2 API接口定义规范

**在services/http目录下按模块定义API接口**

```javascript
// services/http/api.js - 通用业务接口
import mock from '@/utils/mock.js';

/**
 * 获取用户信息
 * @description 根据用户ID获取用户详细信息
 * @param {Object} data - 请求参数
 * @param {string} data.userId - 用户ID
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回用户信息
 */
export function getBasicInfo(data, config) {
  return uni.$petro.http('user.getBasicInfo.h5', data, {
    ...config,
    mockResponse: mock.getBasicInfo
  });
}

/**
 * 获取员工信息
 * @description 根据员工ID获取员工详细信息
 * @param {Object} data - 请求参数
 * @param {string} data.staffId - 员工ID
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回员工信息
 */
export function getStaffInfo(data, config) {
  return uni.$petro.http('user.getStaffInfo.h5', data, {
    ...config,
    mockResponse: mock.getStaffInfo
  });
}

/**
 * 初始化实人认证
 * @description 初始化实人认证接口
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回初始化结果
 */
export function initRealPersonIdentify(data, config) {
  return uni.$petro.http('user.memberInfo.initRealPersonIdentify.h5', data, {
    ...config,
    mockResponse: mock.initRealPersonIdentify
  });
}
```

### 1.3 API接口导出规范

**在services/http/index.js中统一导出所有API接口**

```javascript
// services/http/index.js
import * as api from './api';
import * as user from './user';
import * as order from './order';
import * as payment from './payment';

// 统一导出
export default {
  ...api,
  ...user,
  ...order,
  ...payment
};

// 使用示例
import http from '@/services/http';

// 调用API
const result = await http.getBasicInfo({ userId: '123' });
```

## 规则2 请求参数规范

### 2.1 请求参数验证

**API函数内部必须进行参数验证**

```javascript
/**
 * 创建订单
 * @description 创建新订单
 * @param {Object} data - 请求参数
 * @param {string} data.productId - 产品ID
 * @param {number} data.quantity - 数量
 * @param {string} data.addressId - 地址ID
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回创建结果
 */
export function createOrder(data, config) {
  // 参数验证
  if (!data.productId) {
    return Promise.reject(new Error('产品ID不能为空'));
  }
  
  if (!data.quantity || data.quantity <= 0) {
    return Promise.reject(new Error('数量必须大于0'));
  }
  
  if (!data.addressId) {
    return Promise.reject(new Error('地址ID不能为空'));
  }
  
  // 发起请求
  return uni.$petro.http('order.create.h5', data, {
    ...config,
    mockResponse: mock.createOrder
  });
}
```

### 2.2 请求参数处理

**API函数内部必须进行参数处理**

```javascript
/**
 * 获取订单列表
 * @description 分页获取订单列表
 * @param {Object} data - 请求参数
 * @param {number} data.pageNum - 页码，从1开始
 * @param {number} data.pageSize - 每页数量
 * @param {string} data.status - 订单状态（可选）
 * @param {string} data.keyword - 搜索关键词（可选）
 * @param {string} data.startDate - 开始日期（可选）
 * @param {string} data.endDate - 结束日期（可选）
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回订单列表
 */
export function getOrderList(data, config) {
  // 参数处理
  const params = {
    // 确保页码和每页数量有效
    pageNum: Math.max(1, parseInt(data.pageNum) || 1),
    pageSize: Math.min(100, Math.max(1, parseInt(data.pageSize) || 10)),
    
    // 处理可选参数
    status: data.status || '',
    keyword: (data.keyword || '').trim(),
    
    // 处理日期参数
    startDate: data.startDate || '',
    endDate: data.endDate || ''
  };
  
  // 移除空值参数
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });
  
  // 发起请求
  return uni.$petro.http('order.getList.h5', params, {
    ...config,
    mockResponse: mock.getOrderList
  });
}
```

## 规则3 请求配置规范

### 3.1 基本配置参数

**uni.$petro.http方法的配置参数规范**

```javascript
/**
 * 获取用户信息
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回用户信息
 */
export function getUserInfo(data, config) {
  return uni.$petro.http('user.getInfo.h5', data, {
    // 是否显示加载提示
    showLoading: true,
    
    // 加载提示文本
    loadingText: '加载中...',
    
    // 是否自动显示错误提示
    showError: true,
    
    // 错误提示标题
    errorTitle: '获取用户信息失败',
    
    // 请求超时时间（毫秒）
    timeout: 30000,
    
    // 是否携带Token
    withToken: true,
    
    // 是否包含敏感字段
    riskField: false,
    
    // Mock响应数据
    mockResponse: mock.getUserInfo,
    
    // 合并外部配置
    ...config
  });
}
```

### 3.2 常见场景配置

**不同场景下的配置示例**

```javascript
// 场景1：静默请求（不显示加载和错误提示）
export function silentRequest(data, config) {
  return uni.$petro.http('api.silent.h5', data, {
    showLoading: false,
    showError: false,
    ...config
  });
}

// 场景2：包含敏感信息的请求
export function submitUserInfo(data, config) {
  return uni.$petro.http('user.submit.h5', data, {
    showLoading: true,
    loadingText: '提交中...',
    riskField: true,  // 标记包含敏感字段
    ...config
  });
}

// 场景3：上传文件请求
export function uploadFile(data, config) {
  return uni.$petro.http('file.upload.h5', data, {
    showLoading: true,
    loadingText: '上传中...',
    timeout: 60000,  // 延长超时时间
    ...config
  });
}

// 场景4：不需要Token的公开请求
export function getPublicData(data, config) {
  return uni.$petro.http('public.getData.h5', data, {
    withToken: false,  // 不携带Token
    ...config
  });
}
```

## 规则4 响应处理规范

### 4.1 统一的响应结构

**API响应必须遵循统一的结构**

```javascript
// 标准响应结构
{
  // 请求是否成功
  success: true,
  
  // 业务数据
  data: {
    // 具体业务数据
  },
  
  // 错误信息（success为false时）
  message: '',
  
  // 错误码（success为false时）
  code: 0
}

// 使用示例
async function fetchUserData() {
  try {
    const { success, data, message, code } = await uni.$petro.http('user.getInfo.h5', {
      userId: '123'
    });
    
    if (success) {
      // 处理成功响应
      return data;
    } else {
      // 处理失败响应
      console.error(`获取用户信息失败: ${message}, 错误码: ${code}`);
      throw new Error(message || '获取用户信息失败');
    }
  } catch (error) {
    // 处理异常
    console.error('请求异常:', error);
    throw error;
  }
}
```

### 4.2 分页数据结构

**分页接口响应必须遵循统一的结构**

```javascript
// 分页响应结构
{
  success: true,
  data: {
    // 数据列表
    list: [
      // 列表项
    ],
    
    // 总数
    total: 100,
    
    // 当前页码
    pageNum: 1,
    
    // 每页数量
    pageSize: 10,
    
    // 总页数
    pages: 10
  }
}

// 使用示例
async function fetchOrderList(pageNum = 1, pageSize = 10) {
  try {
    const { success, data } = await uni.$petro.http('order.getList.h5', {
      pageNum,
      pageSize
    });
    
    if (success) {
      // 解构分页数据
      const { list, total, pageNum, pageSize, pages } = data;
      
      // 更新状态
      this.orderList = list;
      this.pagination = {
        current: pageNum,
        pageSize,
        total,
        pages
      };
      
      return list;
    } else {
      throw new Error('获取订单列表失败');
    }
  } catch (error) {
    console.error('获取订单列表失败:', error);
    throw error;
  }
}
```

## 规则5 错误处理规范

### 5.1 统一的错误处理机制

**实现统一的错误处理机制**

```javascript
// 在main.js中配置全局错误处理
import Vue from 'vue';

// 全局错误处理
uni.$petro.errorHandler = (error, config) => {
  console.error('API请求错误:', error);
  
  // 判断是否需要显示错误提示
  if (config?.showError !== false) {
    uni.showToast({
      title: error.message || '请求失败，请重试',
      icon: 'none',
      duration: 2000
    });
  }
  
  // 根据错误码处理特定错误
  if (error.code === 401) {
    // Token过期，跳转到登录页
    uni.$petro.route({
      url: '/pages/login/login',
      type: 'redirectTo'
    });
  } else if (error.code === 403) {
    // 权限不足
    uni.showToast({
      title: '您没有权限执行此操作',
      icon: 'none'
    });
  } else if (error.code === 500) {
    // 服务器错误
    uni.showToast({
      title: '服务器错误，请稍后重试',
      icon: 'none'
    });
  }
};

// 使用示例
async function fetchData() {
  try {
    const { success, data } = await uni.$petro.http('api.getData.h5', {});
    
    if (success) {
      return data;
    }
    
    // 失败响应会自动被errorHandler处理
  } catch (error) {
    // 异常会自动被errorHandler处理
    // 这里可以添加额外的错误处理逻辑
  }
}
```

### 5.2 业务错误处理

**处理特定业务场景的错误**

```javascript
/**
 * 提交订单
 * @param {Object} data - 订单数据
 * @returns {Promise<Object>} 提交结果
 */
export async function submitOrder(data) {
  try {
    const { success, data: responseData, message, code } = await uni.$petro.http('order.submit.h5', data, {
      showLoading: true,
      loadingText: '提交中...',
      // 禁用全局错误处理，自行处理错误
      showError: false
    });
    
    if (success) {
      return responseData;
    }
    
    // 处理特定业务错误
    switch (code) {
      case 10001:
        // 库存不足
        uni.showModal({
          title: '库存不足',
          content: '您选择的商品库存不足，请减少购买数量或选择其他商品',
          showCancel: false
        });
        break;
        
      case 10002:
        // 优惠券无效
        uni.showModal({
          title: '优惠券无效',
          content: '您使用的优惠券已过期或不适用于当前商品',
          showCancel: false
        });
        break;
        
      case 10003:
        // 地址无效
        uni.showModal({
          title: '地址无效',
          content: '您选择的收货地址无效，请重新选择',
          confirmText: '去选择',
          success: (res) => {
            if (res.confirm) {
              uni.$petro.route({
                url: '/pages/address/address',
                type: 'navigateTo'
              });
            }
          }
        });
        break;
        
      default:
        // 其他错误
        uni.showToast({
          title: message || '提交订单失败，请重试',
          icon: 'none'
        });
    }
    
    // 返回错误信息
    return Promise.reject({ message, code });
  } catch (error) {
    console.error('提交订单失败:', error);
    
    // 显示错误提示
    uni.showToast({
      title: error.message || '提交订单失败，请重试',
      icon: 'none'
    });
    
    // 抛出错误
    throw error;
  }
}
```

## 规则6 Mock数据规范

### 6.1 Mock数据组织

**在utils/mock.js中统一管理Mock数据**

```javascript
// utils/mock.js
export default {
  // 获取用户信息
  getBasicInfo: {
    success: true,
    data: {
      id: 'mock-user-001',
      name: '张三',
      mobile: '13800138000',
      avatar: 'https://example.com/avatar.png',
      gender: 1,
      birthday: '1990-01-01',
      points: 1000,
      level: 2,
      levelName: '黄金会员'
    }
  },
  
  // 获取员工信息
  getStaffInfo: {
    success: true,
    data: {
      id: 'mock-staff-001',
      name: '李四',
      mobile: '13900139000',
      avatar: 'https://example.com/staff-avatar.png',
      department: '技术部',
      position: '工程师',
      joinDate: '2020-01-01',
      permissions: ['user:view', 'user:edit']
    }
  },
  
  // 获取订单列表
  getOrderList: {
    success: true,
    data: {
      list: Array(10).fill(0).map((_, index) => ({
        id: `mock-order-${index + 1}`,
        orderNo: `ORD${Date.now()}${index + 1}`,
        amount: Math.floor(Math.random() * 10000) / 100,
        status: ['pending', 'paid', 'shipped', 'completed', 'cancelled'][Math.floor(Math.random() * 5)],
        createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        products: [
          {
            id: `mock-product-${index + 1}`,
            name: `商品${index + 1}`,
            price: Math.floor(Math.random() * 1000) / 100,
            quantity: Math.floor(Math.random() * 5) + 1
          }
        ]
      })),
      total: 100,
      pageNum: 1,
      pageSize: 10,
      pages: 10
    }
  },
  
  // 初始化实人认证
  initRealPersonIdentify: {
    success: true,
    data: {
      verifyId: 'mock-verify-001',
      expireTime: Date.now() + 30 * 60 * 1000,
      verifyUrl: 'https://example.com/verify'
    }
  },
  
  // 实人认证
  realPersonIdentify: {
    success: true,
    data: {
      verified: true,
      score: 95,
      idCard: '110101199001011234',
      name: '张三',
      gender: 1,
      address: '北京市朝阳区',
      idCardFront: 'https://example.com/id-front.png',
      idCardBack: 'https://example.com/id-back.png',
      faceImage: 'https://example.com/face.png'
    }
  },
  
  // 开通车牌账户
  openCarLicenceAccountApi: {
    success: true,
    data: {
      accountId: 'mock-account-001',
      licencePlate: '京A12345',
      balance: 0,
      status: 'active',
      createTime: new Date().toISOString()
    }
  }
};
```

### 6.2 Mock数据使用

**在API函数中使用Mock数据**

```javascript
/**
 * 获取用户信息
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回用户信息
 */
export function getBasicInfo(data, config) {
  return uni.$petro.http('user.getBasicInfo.h5', data, {
    ...config,
    // 指定Mock响应数据
    mockResponse: mock.getBasicInfo
  });
}

/**
 * 获取订单列表
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回订单列表
 */
export function getOrderList(data, config) {
  // 根据参数动态生成Mock数据
  const mockData = {
    success: true,
    data: {
      list: Array(data.pageSize || 10).fill(0).map((_, index) => ({
        id: `mock-order-${(data.pageNum - 1) * (data.pageSize || 10) + index + 1}`,
        orderNo: `ORD${Date.now()}${index + 1}`,
        amount: Math.floor(Math.random() * 10000) / 100,
        status: data.status || ['pending', 'paid', 'shipped', 'completed', 'cancelled'][Math.floor(Math.random() * 5)],
        createTime: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      })),
      total: 100,
      pageNum: parseInt(data.pageNum) || 1,
      pageSize: parseInt(data.pageSize) || 10,
      pages: Math.ceil(100 / (parseInt(data.pageSize) || 10))
    }
  };
  
  return uni.$petro.http('order.getList.h5', data, {
    ...config,
    // 使用动态生成的Mock数据
    mockResponse: mockData
  });
}
```

## 规则7 API调用最佳实践

### 7.1 组件中的API调用

**在Vue组件中调用API的最佳实践**

```vue
<template>
  <view class="user-profile">
    <!-- 加载状态 -->
    <view v-if="loading" class="loading">
      <u-loading mode="circle"></u-loading>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="error" class="error">
      <view class="error-message">{{ error }}</view>
      <u-button @click="fetchUserInfo">重试</u-button>
    </view>
    
    <!-- 数据展示 -->
    <view v-else-if="userInfo" class="user-info">
      <image class="avatar" :src="userInfo.avatar"></image>
      <view class="name">{{ userInfo.name }}</view>
      <view class="mobile">{{ userInfo.mobile }}</view>
      <view class="level">{{ userInfo.levelName }}</view>
    </view>
  </view>
</template>

<script>
import { getBasicInfo } from '@/services/http/api';

export default {
  data() {
    return {
      userInfo: null,
      loading: false,
      error: null
    };
  },
  
  onLoad() {
    this.fetchUserInfo();
  },
  
  methods: {
    /**
     * 获取用户信息
     */
    async fetchUserInfo() {
      try {
        this.loading = true;
        this.error = null;
        
        const { success, data, message } = await getBasicInfo({}, {
          // 禁用全局加载提示，使用组件内的加载状态
          showLoading: false
        });
        
        if (success) {
          this.userInfo = data;
        } else {
          this.error = message || '获取用户信息失败';
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        this.error = error.message || '获取用户信息失败';
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>
```

### 7.2 Store中的API调用

**在Vuex Store中调用API的最佳实践**

```javascript
// services/store/user.js
import { getBasicInfo, updateUserInfo } from '@/services/http/api';

export default {
  namespaced: true,
  
  state: {
    userInfo: null,
    loading: false,
    error: null
  },
  
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
    },
    
    SET_LOADING(state, loading) {
      state.loading = loading;
    },
    
    SET_ERROR(state, error) {
      state.error = error;
    }
  },
  
  actions: {
    /**
     * 获取用户信息
     */
    async fetchUserInfo({ commit }) {
      try {
        commit('SET_LOADING', true);
        commit('SET_ERROR', null);
        
        const { success, data, message } = await getBasicInfo({}, {
          // 禁用全局加载提示，使用Store中的加载状态
          showLoading: false,
          // 禁用全局错误提示，使用Store中的错误状态
          showError: false
        });
        
        if (success) {
          commit('SET_USER_INFO', data);
          return { success, data };
        } else {
          commit('SET_ERROR', message || '获取用户信息失败');
          return { success, message };
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        commit('SET_ERROR', error.message || '获取用户信息失败');
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    },
    
    /**
     * 更新用户信息
     */
    async updateUserInfo({ commit, state }, userData) {
      try {
        commit('SET_LOADING', true);
        commit('SET_ERROR', null);
        
        const { success, data, message } = await updateUserInfo(userData, {
          showLoading: true,
          loadingText: '更新中...'
        });
        
        if (success) {
          // 合并现有用户信息和更新的数据
          const updatedUserInfo = { ...state.userInfo, ...userData };
          commit('SET_USER_INFO', updatedUserInfo);
          return { success, data };
        } else {
          commit('SET_ERROR', message || '更新用户信息失败');
          return { success, message };
        }
      } catch (error) {
        console.error('更新用户信息失败:', error);
        commit('SET_ERROR', error.message || '更新用户信息失败');
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  },
  
  getters: {
    // 是否已登录
    isLoggedIn: state => !!state.userInfo,
    
    // 用户姓名
    userName: state => state.userInfo?.name || '未登录',
    
    // 用户等级
    userLevel: state => state.userInfo?.levelName || '普通会员'
  }
};
```

### 7.3 批量请求处理

**处理批量请求的最佳实践**

```javascript
/**
 * 初始化首页数据
 * @returns {Promise<Object>} 返回首页数据
 */
export async function initHomeData() {
  try {
    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true
    });
    
    // 并行请求多个接口
    const [userResult, bannerResult, noticeResult] = await Promise.all([
      getBasicInfo({}, { showLoading: false }),
      getBannerList({}, { showLoading: false }),
      getNoticeList({}, { showLoading: false })
    ]);
    
    // 处理结果
    const result = {
      success: userResult.success && bannerResult.success && noticeResult.success,
      data: {
        user: userResult.success ? userResult.data : null,
        banners: bannerResult.success ? bannerResult.data.list : [],
        notices: noticeResult.success ? noticeResult.data.list : []
      }
    };
    
    return result;
  } catch (error) {
    console.error('初始化首页数据失败:', error);
    
    // 显示错误提示
    uni.showToast({
      title: '加载失败，请重试',
      icon: 'none'
    });
    
    throw error;
  } finally {
    // 隐藏加载提示
    uni.hideLoading();
  }
}
```

## 总结

本规范文档涵盖了企业小程序前端项目的API接口规范，包括接口封装、Mock数据、错误处理和请求配置等内容。开发人员必须严格遵循这些规范，确保API调用的一致性、可维护性和可扩展性。

**重要提醒：**
1. 所有API请求必须使用uni.$petro.http方法
2. API函数内部必须进行参数验证和处理
3. 实现统一的错误处理机制
4. 在utils/mock.js中统一管理Mock数据
5. 在组件和Store中正确处理API调用的加载状态和错误状态
6. 使用Promise.all处理并行请求
7. 遵循统一的响应结构和分页数据结构
