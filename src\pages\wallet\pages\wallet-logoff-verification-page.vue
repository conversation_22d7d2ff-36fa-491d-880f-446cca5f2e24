<template>
  <view class="face-recognition-page">
    <u-navbar title="安全验证" :autoBack="true" :placeholder="true"></u-navbar>

    <div class="container-box">
      <div class="zyzx-page-face-recognition">
        <div class="container">
          <div class="content">
            <div class="content-header">
              <div class="content-header-des">请识别本人人脸，保持正对手机，确保光线充足</div>
              <div class="content-header-title">
                <span>{{ userInfo.splitterName }}</span>
                <span>{{ userInfo.identityNo }}</span>
              </div>
            </div>

            <img class="content-face" src="@/static/bg-face.png" alt="" />

            <div class="content-example">
              <div class="content-example-cell" v-for="(v, i) in exampleList" :key="i">
                <img class="content-example-cell-img" :src="v.url" alt="" />
                <span class="content-example-cell-title">{{ v.title }}</span>
              </div>
            </div>
          </div>

          <div class="instruction">
            <p class="instruction-title">温馨提示:</p>
            <p class="instruction-des">
              通过人脸识别对您的身份进行确认，以确保企业和个人财产安全，认证后本平台不会对外显示您的个人信息，如您所在企业有核对需求，也仅会显示您的脱敏信息（如张**、110101********0025）</p>
          </div>
        </div>

        <div class="btns">
          <div class="btns-item">
            <button @click="handleSubmit">开始验证</button>
          </div>
        </div>
      </div>
    </div>

    <root-portal :enable="true">
      <zyzx-permission :show="permissionShow" :permissionName="'camera'"
        @cancle="permissionShow = false"></zyzx-permission>
    </root-portal>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import { getBasicInfo, getStaffInfo, initRealPersonIdentify, realPersonIdentify, openCarLicenceAccountApi } from '@/services/http/api.js';
import { walletLogoff } from '@/services/http/account.js';

export default {
  name: 'wallet-logoff-verification-page',
  data() {
    return {
      isReal: false, // 是否实名
      isLoading: false,
      authenticationInfo: null, // 人脸初始化认证结果
      identityNo: '', // 身份证号码

      exampleList: [
        {
          title: '正对手机',
          url: require('@/static/icon-phone.png'),
        },
        {
          title: '光线充足',
          url: require('@/static/icon-shine.png'),
        },
        {
          title: '放慢动作',
          url: require('@/static/icon-time.png'),
        },
      ],
      onAppShow: null,
      onAppHide: null,
      carList: [],
      staffInfoData: {}, // 员工信息
      // 权限弹窗
      permissionShow: false,
    };
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      userInfo: state => state?.roles?.userInfo,
      companyInfo: state => state?.company?.companyInfo,
      accountInfo: state => state?.company?.accountInfo,
      walletInfo: state => state.account.walletInfo,
    }),
  },
  onLoad(options) {
    this.carList = JSON.parse(options.data).carList;
    try {
      this.getUserInfo();
      // * 调用 SDK 注册前逻辑判断
      // this.getStaffInfo();
    } catch (err) {
      console.log(err);
    }
  },
  onShow() { },

  methods: {
    /**
     * @description: 开始验证
     * @return {*}
     */
    handleSubmit() {
      uni.$u.throttle(this.handleSdkGetMetaInfo, 500);
    },
    /**
     * @description: 获取用户信息
     * @return {*}
     */
    async getUserInfo() {
      try {
        const res = getBasicInfo({
          type: 1, // 1-不脱敏信息 2-脱敏信息
        });
        console.log('🚀 ~ getUserInfo ~ res:', res);
        this.identityNo = res?.data?.identityNo || '';
      } catch (err) {
        console.log(err);
      }
    },
    /**
     * 获取员工信息 -- petroChinaNo：档案编号
     * @returns 返回异步操作的结果，无返回值则为 Promise<void>
     */
    async getStaffInfo() {
      try {
        const res = await getStaffInfo({});
        let petroChinaNo = res?.data?.petroChinaNo || '';
        this.staffInfoData = res?.data;
        if (!petroChinaNo) return (this.isReal = false);
        this.isReal = true;
        this.handleSdkAccountRegister(petroChinaNo);
      } catch (err) {
        console.error(err);
      }
    },
    /**
     * 账户SDk注册
     *
     * @param petroChinaNo 档案编号
     * @returns 无返回值
     */
    async handleSdkAccountRegister(petroChinaNo) {
      try {
        let res = await uni.$petro.AccountPlugin.caller({
          bizType: 'register', // 会员注册登录
          data: {
            memberDocNo: petroChinaNo, // 会员编号
            token: (await uni.$petro.getTokenInfo())?.gsmsToken, // 网络认证
            extInfo: {}, // 扩展数据
            headers: {}, // HTTP-Header数据
          },
        });
        console.log('🚀 ~ handleSdkAccountRegister ~ res:', res);
        if (!res || !res.success) return;
      } catch (err) {
        console.error(err);
      }
    },
    // 权限-获取权限
    getPermissions() {
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera'],
        });
        resolve(res);
      });
    },
    /**
     * 获取人脸识别元信息
     */
    async handleSdkGetMetaInfo() {
      try {
        let permission = await this.getPermissions();
        if (!permission.camera) {
          if (uni.$petro.store.systemInfo.osName == 'android' && permission.cameraDoNotAskAgain < 2) return;
          if (uni.$petro.store.systemInfo.osName == 'ios' && permission.isFirst == 1) return;
          this.permissionShow = true;
          return;
        }

        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'getMetaInfo',
          data: {},
        });
        console.log('🚀 ~ handleSdkGetMetaInfo ~ res:', res);

        this.initAuthentication(res.data?.metaInfo);
      } catch (err) {
        console.error(err);
      }
    },
    /**
     * 初始化人脸认证
     *
     * @param metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
     * @param metaInfo.areaCode 认证地市编码，例如：320100
     * @param metaInfo.roleBusinessList 业务角色列表
     * @param metaInfo.roleBusinessList.orgCode 单位编号
     * @param metaInfo.roleBusinessList.staffNo 员工编号
     * @param metaInfo.roleBusinessList.businessNo 业务编号
     * @param metaInfo.roleBusinessList.userRole 角色编码:
     * - 1: 企业管理员
     * - 2: 业务联系人
     * - 3: 员工
     * - 4: 司机
     * - 5: 游客
     */
    async initAuthentication(metaInfo) {
      /**
       * metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
       * areaCode 认证地市编码 例如：320100
       * roleBusinessList 业务角色列表
       *  - orgCode 单位编号
       *  - staffNo 员工编号
       *  - businessNo 业务编号
       *  - userRole 角色编码:1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
       */
      let params = {
        metaInfo: metaInfo,
        areaCode: '510100',
        roleBusinessList: [
          {
            orgCode: this.companyInfo?.enterpriseNo,
            staffNo: this.userInfo?.staffNo,
            businessNo: this.companyInfo?.businessNo,
            userRole: uni.$petro.Enum?.MEMBER_ROLES_CODE[this.role?.code], // 车队业务管理员-2 司机-4
          },
        ],
      };
      console.log('🚀 ~ initAuthentication ~ params:', params);
      try {
        const res = await initRealPersonIdentify(params);
        console.log('🚀 ~ initAuthentication ~ res:', JSON.stringify(res));

        if (!res?.data) return uni.showToast({ content: res.message || '初始化失败' });

        this.authenticationInfo = res.data;
        this.handleSdkVerify();
      } catch (err) {
        console.error(err);
      }
    },
    /**
     * 人脸识别验证
     *
     * @returns Promise<void>
     */
    async handleSdkVerify() {
      try {
        // 获取人脸识别凭证
        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'verify',
          data: {
            certifyId: this.authenticationInfo?.certifyId,
          },
        });
        console.log('🚀 ~ handleSdkVerify ~ res:', JSON.stringify(res));

        this.realPersonAuthentication();
      } catch (err) {
        console.error(err);
      }
    },
    /**
     * 查询实人认证结果
     */
    async realPersonAuthentication() {
      try {
        /**
         * type 实人认证场景： 1—业务资料审核实人; 2—管理员激活企业; 3—司机首次注册; 4—扫一扫实人;
         * verifyNo 身份认证唯一标识(初始化实人认证接口返回)；
         * certifyId 实人认证三方系统的标识(初始化实人认证接口返回)。
         * roleBusinessList 业务角色列表
         *  - orgCode 单位编号
         *  - staffNo 员工编号
         *  - businessNo 业务编号
         *  - userRole 角色编码: 1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
         */
        const { success, data } = await realPersonIdentify({
          type: '1',
          verifyNo: this.authenticationInfo?.verifyNo,
          certifyId: this.authenticationInfo?.certifyId,
          roleBusinessList: this.authenticationInfo?.roleBusinessList,
        });
        console.log('🚀 ~ realPersonAuthentication ~ data:', JSON.stringify(data));

        if (!success || !data?.authInfo) return uni.showToast({ content: message || '实人认证失败' });

        this.walletLogoff();
      } catch (err) {
        console.error(err);
      } finally {
        // this.isLoading = false;
      }
    },
    async walletLogoff() {
      try {
        const params = {
          verifyNo: this.authenticationInfo?.verifyNo, // 人脸认证信息
          staffRole: 2, // 员工角色 车队管理员
          unitMainAccountNo: this.accountInfo?.enterpriseAccountNo, // 单位主账户编号
          mainAccountNo: this.accountInfo?.mainAccountNo, // 主账户编号
          businessNo: this.accountInfo?.businessNo, // 业务编号
        };
        console.log('🚀 ~ walletLogoff ~ params:', params);
        const { success, data } = await walletLogoff(params);
        if (success) {
          uni.$petro.route({ url: '/pages/wallet/pages/wallet-logoff-success-page', type: 'redirectTo', params: { msg: data } }, true);
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.face-recognition-page {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background: #f0f1f5;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
}

.container-box {
  flex: 1;
  overflow: scroll;

  .zyzx-page-face-recognition {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    flex-direction: column;

    .container {
      .content {
        width: 90%;
        height: 800rpx;
        margin: 0 auto;
        padding: 48rpx 60rpx;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #d9d9d9;

        &-header {
          &-title {
            font-weight: bold;
            font-size: 36rpx;
            color: #333333;
            text-align: center;
            display: flex;
            flex-direction: row;
            justify-content: space-around;
          }

          &-des {
            margin-top: 16rpx;
            font-weight: 500;
            font-size: 24rpx;
            color: #999999;
          }
        }

        &-face {
          width: 280rpx;
          height: 280rpx;
        }

        &-example {
          width: 100%;
          display: flex;
          flex-direction: row;
          justify-content: space-between;

          &-cell {
            display: flex;
            flex-direction: column;

            &-img {
              width: 96rpx;
              height: 96rpx;
            }

            &-title {
              font-weight: 500;
              font-size: 24rpx;
              color: #333333;
            }
          }
        }
      }

      .instruction {
        padding: 16rpx 32rpx;
        box-sizing: border-box;

        &-title {
          font-weight: bold;
          font-size: 24rpx;
          color: #666666;
          line-height: 44rpx;
          margin-top: 30rpx;
        }

        &-des {
          font-weight: 500;
          font-size: 24rpx;
          color: #999999;
          line-height: 44rpx;
        }
      }
    }

    .btns {
      flex: 1;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;

      &-item {
        height: 148rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        box-sizing: content-box;
        padding-bottom: env(safe-area-inset-bottom);

        button {
          width: 654rpx;
          height: 92rpx;
          font-weight: 500;
          font-size: 32rpx;
          color: #fff;
          background-color: #fa1919;
          line-height: 92rpx;
          border-radius: 200rpx 200rpx 200rpx 200rpx;
          border: 2rpx solid #eeeeee;
        }
      }
    }

    .btnDisable {
      opacity: 0.5;
    }
  }
}
</style>
