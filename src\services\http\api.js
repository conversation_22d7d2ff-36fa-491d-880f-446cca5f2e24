import mock from '@/utils/mock.js';

// 5.1.19 [获取用户信息/user/getBasicInfo]  GT
export function getBasicInfo(data, config) {
  return uni.$petro.http('user.getBasicInfo.h5', data, {
    ...config,
    mockResponse: mock.getBasicInfo,
  });
}

// 5.1.18 [获取员工信息/user/getStaffInfo]  GT
export function getStaffInfo(data, config) {
  return uni.$petro.http('user.getStaffInfo.h5', data, {
    ...config,
    mockResponse: mock.getStaffInfo,
  });
}

// 5.1.12 [初始化实人认证接口 /user/memberInfo/initRealPersonIdentify] GT
export function initRealPersonIdentify(data, config) {
  return uni.$petro.http('user.memberInfo.initRealPersonIdentify.h5', data, {
    ...config,
    mockResponse: mock.initRealPersonIdentify,
  });
}

// 5.1.13 [实人认证 /user/memberInfo/realPersonIdentify]  GT
export function realPersonIdentify(data, config) {
  return uni.$petro.http('user.memberInfo.realPersonIdentify.h5', data, {
    ...config,
    mockResponse: mock.realPersonIdentify,
  });
}

// 5.1.4 [开通车牌账户 /account/openCarLicenceAccount] GT
export function openCarLicenceAccountApi(data, config) {
  return uni.$petro.http('account.openCarLicenceAccount.h5', data, {
    ...config,
    mockResponse: mock.openCarLicenceAccountApi,
  });
}

// 5.1.3 [手机号注销用户 /account/manager/cancelUser] GT
export function cancelUserApi(data, config) {
  return uni.$petro.http('account.manager.cancelUser.h5', data, {
    ...config,
    mockResponse: mock.openCarLicenceAccountApi,
  });
}