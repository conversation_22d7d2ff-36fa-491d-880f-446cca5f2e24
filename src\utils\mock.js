export default {
  refuelCountApi: {
    success: true,
    data: [
      {
        refuelDate: '2025-04-21',
        refuelQty: 12.36,
      },
      {
        refuelDate: '2025-04-21',
        refuelQty: 24.36,
      },
      {
        refuelDate: '2025-04-21',
        refuelQty: 54.36,
      },
      {
        refuelDate: '2025-04-22',
        refuelQty: 74.36,
      },
      {
        refuelDate: '2025-04-23',
        refuelQty: 24.36,
      },
    ],
    message: '请求成功',
    errorCode: null,
  },
  consumptionCountApi: {
    success: true,
    data: [
      {
        consumptionDate: '2025-04-21',
        consumptionAmount: 123124.36,
      },
      {
        consumptionDate: '2025-04-22',
        consumptionAmount: 933212.32,
      },
      {
        consumptionDate: '2025-04-24',
        consumptionAmount: 34233.32,
      },
    ],
    message: '请求成功',
    errorCode: null,
  },
  getLimitationApi: {
    success: true,
    data: {
      stationLimit: '金霞站', // 定点油站
      onceOilVolume: '10', // 每次加油升数
      dayOilVolume: '20', // 每天加油升数
      dayOilTimes: '100', // 每天加油次数
      dayAmount: '5000', //  每天消费金额
      goods: '氢气', // 限制商品列表
      oils: '氢气', // 限制油品列表
      frequency: '5', // 加油频次限制
    },
    message: '请求成功',
    errorCode: null,
  },
  queryDriverInfoList: {
    success: true,
    data: {
      usedCount: 0,
      noUsedCount: 1,
      groupByFirstName: {
        Z: [
          {
            firstName: 'Z',
            staffNo: '25241023103004304',
            enterpriseNo: '2430100386810001',
            businessNo: '2024102300013671',
            phone: '***********',
            identityType: 1,
            identityNo: '230811199506303524',
            realName: '郑军哲',
            licensePlateList: ['京Q11111'],
            usedFlag: 0,
            roleBusinessStatus: 5,
            createTime: '2024-10-23 10:30:46',
          },
        ],
      },
    },
    message: '请求成功',
    errorCode: null,
  },
  queryDriverInfo: {
    success: true,
    data: {
      staffNo: '20240814154456282',
      enterpriseNo: '2000087627600001',
      businessNo: '2024081400012408',
      phone: '***********',
      identityType: 1,
      identityNo: '511621********4177',
      realName: '雷天伟',
      licensePlateList: ['川A99999'],
      usedFlag: null,
      roleBusinessStatus: 1,
      createTime: '2024-08-14 15:44:56',
    },
    message: '请求成功',
    errorCode: null,
  },
  consumeList: {
    success: true,
    data: {
      pageNum: 1,
      totalRows: 1,
      pageSize: 10,
      pageSum: 1,
      rows: [
        {
          orderNo: '2411271648370006775441066',
          stationCode: '1-A5401-C001-S001',
          stationName: '(简)西藏销售拉萨分公司中卫加油站测试中卫',
          businessDay: '2024-11-27',
          orderChannel: 42,
          orderType: 2,
          orderSubType: 11,
          orderStatus: 4,
          licensePlate: null,
          orderTotalAmount: 1.93,
          discountTotalAmount: 0.0,
          payDiscountTotalAmount: 0.02,
          receivedTotalAmount: 1.91,
          actualPayTotalAmount: 1.91,
          createTime: '2024-11-27 16:48:37',
          updateTime: '2024-11-27 16:48:39',
          invoiceFlag: 0,
          createByName: '刘滕',
          mileage: '99.9',
          equipmentCardNo: '***********',
          orderItemList: [
            {
              orderNo: '2411271648370006775441066',
              productName: '95号 车用汽油(Ⅴ)',
              productNo: '300667',
              productType: '1',
              gunNo: 5,
              productQty: 9.39,
              productUnit: 'L',
              unitPrice: '10.66/L',
              discountAmount: 0.0,
              receivableAmount: 1.91,
              payDiscountAmount: 0.0,
              imgUrl: null,
              productCategory: '3100',
              orderItemNo: '270382',
            },
          ],
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  },
  queryBusinessRegionList: {
    success: true,
    data: [
      { upperLetter: 'A', regionList: [{ id: 340000, name: '安徽省' }] },
      { upperLetter: 'B', regionList: [{ id: 110000, name: '北京市' }] },
      { upperLetter: 'C', regionList: [{ id: 500000, name: '重庆市' }] },
      { upperLetter: 'F', regionList: [{ id: 350000, name: '福建省' }] },
      {
        upperLetter: 'G',
        regionList: [
          { id: 440000, name: '广东省' },
          { id: 620000, name: '甘肃省' },
          { id: 450000, name: '广西壮族自治区' },
          { id: 520000, name: '贵州省' },
        ],
      },
      {
        upperLetter: 'H',
        regionList: [
          { id: 130000, name: '河北省' },
          { id: 420000, name: '湖北省' },
          { id: 230000, name: '黑龙江省' },
          { id: 410000, name: '河南省' },
          { id: 430000, name: '湖南省' },
          { id: 460000, name: '海南省' },
        ],
      },
      {
        upperLetter: 'J',
        regionList: [
          { id: 220000, name: '吉林省' },
          { id: 320000, name: '江苏省' },
          { id: 360000, name: '江西省' },
        ],
      },
      { upperLetter: 'L', regionList: [{ id: 210000, name: '辽宁省' }] },
      {
        upperLetter: 'N',
        regionList: [
          { id: 150000, name: '内蒙古自治区' },
          { id: 640000, name: '宁夏回族自治区' },
        ],
      },
      { upperLetter: 'Q', regionList: [{ id: 630000, name: '青海省' }] },
      {
        upperLetter: 'S',
        regionList: [
          { id: 510000, name: '四川省' },
          { id: 370000, name: '山东省' },
          { id: 310000, name: '上海市' },
          { id: 140000, name: '山西省' },
          { id: 610000, name: '陕西省' },
        ],
      },
      { upperLetter: 'T', regionList: [{ id: 120000, name: '天津市' }] },
      {
        upperLetter: 'X',
        regionList: [
          { id: 650000, name: '新疆维吾尔自治区' },
          { id: 540000, name: '西藏自治区' },
        ],
      },
      { upperLetter: 'Y', regionList: [{ id: 530000, name: '云南省' }] },
      { upperLetter: 'Z', regionList: [{ id: 330000, name: '浙江省' }] },
    ],
    message: '请求成功',
    errorCode: null,
  },
  getBasicInfo: {
    success: true,
    data: {
      userId: '233',
      username: '***********',
      firstLoginFlag: 0,
      forceModifyPwdFlag: 1,
      avatar: '',
      gender: 1,
      realname: '雷天伟',
      phone: '***********',
      email: '',
      identityNo: '511621199906074177',
      status: 0,
      disableFlag: 0,
      orgCode: '2000087627600001',
      orgName: '唐山锐意聚机械设备有限公司',
      orgId: null,
      orgType: null,
      petroChinaNo: '35116245792010',
    },
    message: '请求成功',
    errorCode: null,
  },
  orderDetail: {
    success: true,
    data: {
      orderNo: '2411261747290005175341129',
      payChannel: ['RPOS'],
      orderTotalAmount: 14.89,
      actualPayTotalAmount: 14.89,
      discountTotalAmount: 0,
      payDiscountAmount: 0.0,
      orderItemList: [
        {
          orderNo: '2411261747290005175341129',
          productName: '0号 车用柴油(Ⅵ)',
          productNo: '300863',
          productType: '1',
          gunNo: 1,
          productQty: 1.12,
          productUnit: 'L',
          unitPrice: '13.3/L',
          discountAmount: 0.0,
          receivableAmount: 14.89,
          payDiscountAmount: 0.0,
          imgUrl: null,
          productCategory: '3100',
          orderItemNo: '270434',
        },
      ],
      stationName: '湖南长沙金霞加油站',
      stationCode: '1-A4301-C001-S006',
      payItemList: [{ payMethod: null, payAmount: 14.89, payConfirmationTime: null, payMethodName: '昆仑e享卡' }],
      activityDiscountList: null,
      orderType: 2,
      orderSubType: 14,
      discountList: [],
      payDetailList: [{ payAmount: 14.89, payMethod: null, payChannel: null, payMethodName: '昆仑e享卡' }],
      payConfirmationTime: '2024-11-27 17:47:44',
      businessDay: '2024-11-26 00:00:00',
    },
    message: '请求成功',
    errorCode: null,
  },
  getStaffInfo: {},
  initRealPersonIdentify: {
    success: true,
    data: {
      verifyNo: 'VER40730213354025664',
      certifyUrl: null,
      certifyId: 'sha172c7df74137e94e60aa441be5d39',
      code: null,
      roleBusinessList: [{ orgCode: '2000087627600001', staffNo: '20240814154456282', businessNo: '2024081400012408', userRole: 2 }],
      channelType: null,
    },
    message: '请求成功',
    errorCode: null,
  },
  realPersonIdentify: {
    success: true,
    data: {
      authInfo:
        'R3WIFjAx3Pa6DwOoqBRDBI7GTcBcPUd3kEXhKGMuSD_DdwsAmo5d-LfQygDnzvo5SbdwFJHvF4MPAUm56B_tJ_9Esa7mb4-WLktXliCl3hx4BHq5ogTgjgPFMQgBNSrgHl1n51nQ1jdkIg_WhMCN6IHH0S4FkMVMBoYMRkzSNS9FJpaBxjnSo2V-L08mqgI4fbJfRAG-1K0FpcRDWdvz-JcqebgrQWTdTcIoyY5StS_snHZmzK50osph0jIVSzJXmFwJAQMrBhei_l7NU56be-9dHHDKVsEjBmkkP0A6m5aRLCVGDpITLqZYqe6OsV00wyqmnRkds4P7IeLEYNkI2g',
    },
    message: '请求成功',
    errorCode: null,
  },
  cancelDriverAccount: {
    success: true,
    data: {},
    message: '请求成功',
    errorCode: null,
  },
  businessManagerList: {
    success: true,
    data: { detail: [{ name: '雷天伟', phone: '***********', enterpriseName: '唐山锐意聚机械设备有限公司' }] },
    message: '请求成功',
    errorCode: null,
  },
  deleteDriverInfo: {
    success: true,
    data: { success: 2 },
    message: '请求成功',
    errorCode: null,
  },
  getAllocationSummaryTemplateApi: {
    success: true,
    data: {
      pageNum: 1,
      pageSize: 10,
      pageSum: 1,
      totalRows: 2,
      rows: [
        {
          templateNo: ************,
          userId: '536',
          staffNo: null,
          mainAccountNo: '************',
          businessType: 1,
          accountType: 5,
          templateName: '超额',
          templateStatus: 3,
          totalAmount: 3000.0,
          num: null,
          createTime: '2024-12-02 17:06:01',
        },
        {
          templateNo: ************,
          userId: '536',
          staffNo: null,
          mainAccountNo: '************',
          businessType: 1,
          accountType: 5,
          templateName: '分配模板',
          templateStatus: 3,
          totalAmount: 70.0,
          num: null,
          createTime: '2024-12-02 15:31:58',
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  },
  queryDriverAccountListApi: {
    success: true,
    data: {
      L: [
        {
          firstName: 'L',
          memberName: '刘滕',
          staffNo: '*****************',
          memberPhone: '***********',
          cardNo: '************',
          memberDocNo: null,
          businessNo: '****************',
          unitAlias: 'yyds',
          // walletAccountNo: '************',
          // walletFrozenAmount: 0.0,
          // walletAvailableAmount: 407.0,
          // pointsAccountNo: '************',
          // pointsFrozenAmount: '0.0000',
          // pointsAvailableAmount: '0.0000',
          enterpriseAccountNo: '************',
          mainAccountNo: '************',
          enterpriseNo: '****************',
          walletAccountList: [
            {
              cardNo: null,
              accountNo: '************',
              frozenAmount: *********.0,
              availableAmount: ********.0,
              accountStatus: null,
              capitalType: 1,
              accountType: 5,
            },
          ],
          loyaltyAccountList: [
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 5.0,
              accountStatus: 1,
              accountType: 8,
            },
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 8.0,
              accountStatus: 1,
              accountType: 13,
            },
          ],
        },
      ],
      X: [
        {
          firstName: 'X',
          memberName: '许文阳',
          staffNo: '*****************',
          memberPhone: '***********',
          cardNo: '************',
          memberDocNo: null,
          businessNo: '****************',
          unitAlias: 'yyds',
          // walletAccountNo: '************',
          // walletFrozenAmount: 0.0,
          // walletAvailableAmount: 481.52,
          // pointsAccountNo: '************',
          // pointsFrozenAmount: '0.0000',
          // pointsAvailableAmount: '0.0000',
          enterpriseAccountNo: '************',
          mainAccountNo: '************',
          enterpriseNo: '****************',
          walletAccountList: [
            {
              cardNo: null,
              accountNo: '************',
              frozenAmount: *********.0,
              availableAmount: ********.0,
              accountStatus: null,
              capitalType: 1,
              accountType: 5,
            },
          ],
          loyaltyAccountList: [
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 5.0,
              accountStatus: 1,
              accountType: 8,
            },
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 8.0,
              accountStatus: 1,
              accountType: 13,
            },
          ],
        },
      ],
      Z: [
        {
          firstName: 'Z',
          memberName: '张鸾',
          staffNo: '*****************',
          memberPhone: '***********',
          cardNo: '************',
          memberDocNo: null,
          businessNo: '****************',
          unitAlias: 'yyds',
          // walletAccountNo: '************',
          // walletFrozenAmount: 0.0,
          // walletAvailableAmount: 0.0,
          // pointsAccountNo: '************',
          // pointsFrozenAmount: '0.0000',
          // pointsAvailableAmount: '0.0000',
          enterpriseAccountNo: '************',
          mainAccountNo: '************',
          enterpriseNo: '****************',
          walletAccountList: [
            {
              cardNo: null,
              accountNo: '************',
              frozenAmount: *********.0,
              availableAmount: ********.0,
              accountStatus: null,
              capitalType: 1,
              accountType: 5,
            },
          ],
          loyaltyAccountList: [
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 5.0,
              accountStatus: 1,
              accountType: 8,
            },
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 8.0,
              accountStatus: 1,
              accountType: 13,
            },
          ],
        },
        {
          firstName: 'Z',
          memberName: '张海涛',
          staffNo: '*****************',
          memberPhone: '***********',
          cardNo: '************',
          memberDocNo: null,
          businessNo: '****************',
          unitAlias: 'yyds',
          // walletAccountNo: '************',
          // walletFrozenAmount: 0.0,
          // walletAvailableAmount: 900.0,
          // pointsAccountNo: '************',
          // pointsFrozenAmount: '0.0000',
          // pointsAvailableAmount: '0.0000',
          enterpriseAccountNo: '************',
          mainAccountNo: '************',
          enterpriseNo: '****************',
          walletAccountList: [
            {
              cardNo: null,
              accountNo: '************',
              frozenAmount: *********.0,
              availableAmount: ********.0,
              accountStatus: null,
              capitalType: 1,
              accountType: 5,
            },
          ],
          loyaltyAccountList: [
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 5.0,
              accountStatus: 1,
              accountType: 8,
            },
            {
              pointAccountNo: '************',
              frozenPointAmount: 0.0,
              availablePointAmount: 8.0,
              accountStatus: 1,
              accountType: 13,
            },
          ],
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  },
  queryAccountVehicleListApi: {
    success: true,
    data: [
      {
        id: null,
        licensePlate: '川A99999',
        assistantLicensePlate: null,
        licensePlateMainAcctNo: '************',
        vehicleType: null,
        fillType: null,
        fillOilType: null,
        vehicleLicensePhoto: null,
        addTime: null,
        driverList: null,
        staffList: [
          { name: '刘滕', enterpriseStaffNo: '*****************', mainAccountNo: '************' },
          { name: '许文阳', enterpriseStaffNo: '*****************', mainAccountNo: '************' },
          { name: '张鸾', enterpriseStaffNo: '*****************', mainAccountNo: '************' },
        ],
        enterpriseName: null,
        bdCardNo: '***********',
      },
      {
        id: null,
        licensePlate: '川A11111',
        assistantLicensePlate: null,
        licensePlateMainAcctNo: '************',
        vehicleType: null,
        fillType: null,
        fillOilType: null,
        vehicleLicensePhoto: null,
        addTime: null,
        driverList: null,
        staffList: [{ name: '许文阳', enterpriseStaffNo: '*****************', mainAccountNo: '************' }],
        enterpriseName: null,
        bdCardNo: '***********',
      },
    ],
    message: '请求成功',
    errorCode: null,
  },
  queryVehiclePageList: {
    success: true,
    data: {
      usedTotal: null,
      unUsedTotal: null,
      pageNum: 1,
      pageSize: 10,
      pageSum: 3,
      totalRows: 22,
      rows: [
        {
          id: null,
          licensePlate: '川B123469',
          assistantLicensePlate: null,
          licensePlateMainAcctNo: null,
          vehicleType: 2,
          fillType: 3,
          fillOilType: null,
          vehicleLicensePhoto: null,
          addTime: null,
          driverList: null,
          staffList: null,
          enterpriseName: null,
          bdCardNo: '***********',
        },
        {
          id: null,
          licensePlate: '川B123468',
          assistantLicensePlate: null,
          licensePlateMainAcctNo: null,
          vehicleType: 2,
          fillType: 3,
          fillOilType: null,
          vehicleLicensePhoto: null,
          addTime: null,
          driverList: null,
          staffList: null,
          enterpriseName: null,
          bdCardNo: '***********',
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  },
  openCarLicenceAccountApi: {
    success: true,
    data: [
      {
        carLicence: '川A 88881',
        openResult: '开通失败',
      },
    ],
    message: '请求成功',
    errorCode: null,
  },
  getContactsApi: {
    success: true,
    data: {
      enterpriseContacts: [
        {
          name: '雷天伟',
          phone: '183****0904',
          identityNo: '511621********4177',
          enterpriseName: '长沙市岳麓区林雀子饭店',
          accountRemark: '长沙市岳麓区林雀子饭店',
          mobilePhone: '***********',
          staffNo: '*****************',
          role: 2,
          businessNo: '****************',
          enterpriseNo: '****************',
        },
      ],
      enterpriseDriver: [
        {
          name: '刘滕',
          phone: '155****1003',
          identityNo: '510623********1914',
          enterpriseName: '长沙市岳麓区林雀子饭店',
          accountRemark: '长沙市岳麓区林雀子饭店',
          mobilePhone: '***********',
          staffNo: '*****************',
          role: 4,
          businessNo: '****************',
          enterpriseNo: '****************',
        },
        {
          name: '张鸾',
          phone: '156****0835',
          identityNo: '513002********6131',
          enterpriseName: '长沙市岳麓区林雀子饭店',
          accountRemark: '长沙市岳麓区林雀子饭店',
          mobilePhone: '***********',
          staffNo: '*****************',
          role: 4,
          businessNo: '****************',
          enterpriseNo: '****************',
        },
      ],
      enterpriseContactsTotal: 1,
      enterpriseDriverTotal: 2,
    },
    message: '请求成功',
    errorCode: null,
  },
  queryAccountInfoListApi: {
    success: true,
    data: [
      {
        enterpriseAccountNo: '************',
        mainAccountNo: '************',
        enterpriseNo: '****************',
        enterpriseStaffNo: '*****************',
        userId: '469',
        userName: '雷天伟',
        licencePlate: '川A99999',
        unitAlias: '长沙市岳麓区林雀子饭店',
        accountType: 3,
        businessNo: '****************',
        businessType: 10,
        invoiceType: null,
        accountStatus: 1,
        accountPlace: '1-A4301-C001',
        accountPlaceName: '湖南长沙销售分公司',
        remark: null,
        serialNo: null,
        memberName: null,
        memberDocNo: null,
        enterpriseName: null,
        staffNo: null,
        walletAccountList: [
          {
            cardNo: null,
            accountNo: '************',
            frozenAmount: 12311.0,
            availableAmount: 4421.0,
            accountStatus: null,
            capitalType: 1,
            accountType: 5,
          },
        ],
        loyaltyAccountList: [
          {
            pointAccountNo: '************',
            frozenPointAmount: 0.0,
            availablePointAmount: 552.0,
            accountStatus: 1,
            accountType: 8,
          },
          {
            pointAccountNo: '************',
            frozenPointAmount: 0.0,
            availablePointAmount: 1123.0,
            accountStatus: 1,
            accountType: 13,
          },
        ],
      },
      {
        enterpriseAccountNo: '************',
        mainAccountNo: '************',
        enterpriseNo: '****************',
        enterpriseStaffNo: '*****************',
        userId: '469',
        userName: '雷天伟',
        licencePlate: '川A11111',
        unitAlias: '长沙市岳麓区林雀子饭店',
        accountType: 3,
        businessNo: '****************',
        businessType: 10,
        invoiceType: null,
        accountStatus: 1,
        accountPlace: '1-A4301-C001',
        accountPlaceName: '湖南长沙销售分公司',
        remark: null,
        serialNo: null,
        memberName: null,
        memberDocNo: null,
        enterpriseName: null,
        staffNo: null,
        walletAccountList: [
          {
            cardNo: null,
            accountNo: '************',
            frozenAmount: 111.0,
            availableAmount: 222.0,
            accountStatus: null,
            capitalType: 1,
            accountType: 5,
          },
        ],
        loyaltyAccountList: [
          {
            pointAccountNo: '************',
            frozenPointAmount: 0.0,
            availablePointAmount: 5.0,
            accountStatus: 1,
            accountType: 8,
          },
          {
            pointAccountNo: '************',
            frozenPointAmount: 0.0,
            availablePointAmount: 12312.0,
            accountStatus: 1,
            accountType: 13,
          },
        ],
      },
    ],
    message: '请求成功',
    errorCode: null,
  },
  invoiceDetail: {
    success: true,
    data: {
      invoiceInfoList: [
        {
          invoiceCode: '************',
          invoiceNo: '********',
          invoiceIssueDate: '2024-12-11 14:48:21',
          invoiceTypeCode: '026',
          sellerTaxId: '915000190912010295',
          sellerName: '大客户测试盘',
          sellerAddrTel: '北京市海淀区杏石口路甲18号2号楼',
          sellerFinancialAccount: '中国民生银行北京紫竹支行',
          sellerAccount: '*****************',
          buyerNature: '1',
          buyerTaxId: '92410402MA47313F6G',
          buyerName: '平顶山市新华区长兴视立美眼镜销售店',
          buyerFinancialAccount: null,
          buyerAccount: null,
          buyerAddrTel: '00',
          taxInclusiveTotalAmount: '0.03',
          taxTotalAmount: '0.00',
          taxExclusiveTotalAmount: '0.03',
          changeTitleMark: null,
          invoicingStatus: 2,
          orgCode: null,
        },
        {
          invoiceCode: '************',
          invoiceNo: '********',
          invoiceIssueDate: '2024-12-11 14:48:22',
          invoiceTypeCode: '026',
          sellerTaxId: '915000190912010295',
          sellerName: '大客户测试盘',
          sellerAddrTel: '北京市海淀区杏石口路甲18号2号楼',
          sellerFinancialAccount: '中国民生银行北京紫竹支行',
          sellerAccount: '*****************',
          buyerNature: '1',
          buyerTaxId: '92410402MA47313F6G',
          buyerName: '平顶山市新华区长兴视立美眼镜销售店',
          buyerFinancialAccount: null,
          buyerAccount: null,
          buyerAddrTel: '00',
          taxInclusiveTotalAmount: '13.85',
          taxTotalAmount: '1.59',
          taxExclusiveTotalAmount: '12.26',
          changeTitleMark: null,
          invoicingStatus: 2,
          orgCode: null,
        },
      ],
      orderList: ['2412071546480006775413114'],
    },
    message: '请求成功',
    errorCode: null,
  },
};
