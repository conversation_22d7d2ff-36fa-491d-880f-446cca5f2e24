<script>
import { mapState } from 'vuex';

export default {
  name: 'gift-pay',
  computed: {
    ...mapState({
      walletInfo: state => state?.account?.walletInfo,
    }),
  },
  data() {
    return {
      query: {},
      orderInfo: {},
      loading: false,
      pwdPrompt: false,
    };
  },
  onLoad(query) {
    this.query = query;
    try {
      if (this.query?.data) this.orderInfo = JSON.parse(this.query?.data);
    } catch (err) {
      console.error(err);
    }
  },
  methods: {
    // 订单支付
    async orderPay() {
      const orderInfo = this.orderInfo;
      if (!orderInfo?.orderNo) {
        uni.showModal({
          title: '温馨提示',
          content: '订单数据不存在，请重试！',
        });
        return;
      }
      try {
        this.loading = true;
        const res = await uni.$petro.PayPlugin.caller(
          {
            bizType: 'rposBusinessPay', // 企业确认支付扫码订单
            data: {
              businessInfo: {
                businessNo: this.walletInfo?.businessNo, // 业务编码
                staffNo: this.walletInfo?.staffNo, // 员工编号
                accountType: 12, // 账户类型 0 无效，12 礼品卡，3 信用账户，13 油币，14 好客币，5 昆仑E享卡账户，26 加油卡账户，11 优惠券账户，8 积分账户，101 现金券，32 礼品券，33 提货券，
                capitalType: 1, // 金额类型 1--资金 2--信用
                mainAccountNo: this.walletInfo?.mainAccountNo,
                unitMainAccountNo: this.walletInfo?.enterpriseAccountNo,
              },
              payOrderNo: orderInfo?.payOrderNo,
              orderNo: orderInfo?.orderNo,
              amount: orderInfo?.payMoney,
              rechargeType: orderInfo?.rechargeType,
              extInfo: {},
              headers: {},
            },
          },
          {
            ignoreErrs: true,
          },
        );
        if (res?.success) {
          uni.$petro.route(
            {
              url: '/pages/gift-code/pages/gift-result/gift-result',
              params: res.data,
              type: 'redirectTo',
            },
            true,
          );
        } else {
          // 未设置该密码:P_B07_705004
          if (res?.message.indexOf('P_B07_705004') !== -1) {
            this.pwdPrompt = true;
            return;
          }
          // 不抛错-键盘取消:P_SDK07_007000
          if (res?.message?.indexOf('P_SDK07_007000') === -1) {
            uni.showModal({
              title: res?.message || '异常',
              confirmText: '确认',
              showCancel: false,
            });
          }
        }
      } catch (err) {
        console.error(err);
      } finally {
        this.loading = false;
      }
    },
    pwdPromptConfirm() {
      uni.$petro.route({ url: '/pages/wallet/pages/face-recognition-page', type: 'navigateTo', params: {} });
      this.pwdPrompt = false;
    },
  },
};
</script>

<template>
  <div class="page-gift-pay">
    <u-navbar :title="'确认订单'" :autoBack="true" :placeholder="true" :bgColor="'transparent'"></u-navbar>
    <div class="page-container">
      <div class="title">待付{{ orderInfo.payType === 15 ? '积分' : '金额' }}</div>
      <div class="value">
        <span class="unit" v-if="orderInfo.payType !== 15">￥</span>
        <span class="money">{{ orderInfo.payMoney }}</span>
        <span class="unit" v-if="orderInfo.payType === 15">积分</span>
      </div>

      <button class="btn-pay" :class="{ 'is-loading': loading }" :loading="loading" @click="orderPay()">确认支付 </button>
    </div>
    <u-modal
      :show="pwdPrompt"
      title="温馨提示"
      content="未设置支付密码，请设置支付密码。"
      confirmText="去设置"
      cancelText="取消"
      :showCancelButton="true"
      confirmColor="#FA1919"
      width="540rpx"
      @confirm="pwdPromptConfirm"
      @cancel="pwdPrompt = false"
    ></u-modal>
  </div>
</template>

<style scoped lang="scss">
.page-gift-pay {
  .page-container {
    text-align: center;
    color: #ff7033;

    .title {
      font-size: 32rpx;
      margin-top: 100rpx;
      color: #333;
    }

    .unit {
      font-size: 42rpx;
    }

    .money {
      font-size: 76rpx;
    }

    .btn-pay {
      box-sizing: border-box;
      height: 92rpx;
      text-align: center;
      font-weight: 500;
      font-size: 32rpx;
      line-height: 92rpx;
      border-radius: 16rpx;
      background: #fa1919;
      color: #fff;
      padding: 0 120rpx;
      display: inline-block;
      margin-top: 60rpx;
      width: 90%;

      &:active {
        opacity: 0.8;
      }

      &.is-loading {
        opacity: 0.5;
        pointer-events: none;
      }
    }
  }
}
</style>
