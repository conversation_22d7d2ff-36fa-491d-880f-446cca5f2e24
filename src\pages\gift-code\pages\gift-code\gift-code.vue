<template>
  <div class="page-gift-code">
    <div class="header">
      <zyzx-company-bar :color="'#333'" :menu="true" :menu-list="COMPANY_BAR_MENU"></zyzx-company-bar>
    </div>

    <view class="container">
      <view class="code" @click="refreshCode('click')">
        <view class="code-value">
          <canvas canvas-id="qrcode" id="qrcode" style="width: 320rpx; height: 320rpx" />
        </view>
        <div class="code-loading" v-if="loadingCode">
          <u-loading-icon></u-loading-icon>
        </div>
      </view>
    </view>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { QRCode } from '@uni-ui/code-plugs';
import { COMPANY_BAR_MENU } from '@/services/enum';

export default {
  name: 'gift-code',
  components: {},
  props: {
    pageShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      systemInfo: uni.getSystemInfoSync(),
      businessInfo: {
        name: '',
        role: '礼品卡券管理员',
      },
      qrCode: '', // 二维码字符串
      qrCodePollingTaskId: null, // 二维码轮询刷新id
      orderPollingTaskId: null, // 支付订单轮询任务id
      loadingCode: false, // 加载中
      qrCodeInfo: {
        id: 'qrcode',
        // ctx: this,
        code: '', //必传
        level: 4, //纠错等级 0~4 默认4 非必传
        type: 'none', // 码点 目前只支持 none 其它暂不支持 非必传
        // src: '/static/35.png', //画布背景 非必传
        padding: 10, //二维码margin Number 单位rpx 默认0 非必传
        // border: {
        //   //非必传
        //   color: ['#F27121', '#8A2387', '#1b82d2'], //边框颜色支持渐变色 最多10种颜色 如果默认黑色此属性不需要传
        //   opacity: 0.6, //边框透明度 默认为1不透明 0~1
        //   lineWidth: 6, //边框宽度
        //   degree: 15, //边框圆角度数 默认5
        // },
        // text: {
        //   //二维码绘制文字 非必传
        //   opacity: 1, //文字透明度 默认不透明1  0~1 非必传
        //   font: 'bold 20px system-ui', //文字是否加粗 默认normal 20px system-ui 非必传
        //   color: ['#000000'], // 文字颜色 多个颜色支持渐变色 默认黑色 非必传
        //   content: '这是一个测试', //文字内容
        // },
        // img: {
        //   // 二维码log配置 非必传
        //   // src: '/static/logo.png', // 图片地址
        //   size: 40, // 图片大小
        //   degree: 15, // 圆角大小 如果type为round生效
        //   type: 'round', //图片展示类型 默认none 可选值  round圆角  circle圆 如果为round 可以传入degree设置圆角大小 默认 5
        //   color: '#ffffff', //图片周围的白色边框
        //   width: 8, //图片周围白色边框的宽度 默认5
        // },
        // color: ['#11998e', '#38ef7d', '#F27121', '#8A2387', '#1b82d2'], //二维码颜色支持渐变 最多10种颜色 默认黑色 非必传
        // bgColor: '#FFFFFF', //二维码背景色 默认白色 非必传
        size: 320, // 二维码大小 Number 单位rpx 必传
      },
      COMPANY_BAR_MENU,
      shouldRefreshCode: false,
      isInit: false,
    };
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      walletInfo: state => state?.account?.walletInfo,
      tabBarValue: state => state?.tabBar?.tabBarValue,
    }),
    // role() {
    //   const roleInfo = this.$store?.state?.roles?.role;
    //   console.log('roleInfo', JSON.stringify(roleInfo));
    //   if (roleInfo?.businessNo) {
    //     this.getWalletInfo();
    //   }
    //   return roleInfo;
    // },
  },
  async beforeCreate() {
    // uni.$on('updateGiftCardCode', data => {
    //   this.shouldRefreshCode = true;
    // });
  },
  async created() {
    console.log('created');
  },
  async onLoad(query) {
    console.log('onLoad', query);
  },
  onUnload() {
    console.log('onUnload');
    this.clearPollingTasks();
  },
  destroyed() {
    console.log('destroyed');
    this.clearPollingTasks();
  },
  mounted() { },
  watch: {
    tabBarValue: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.checkPollingTasks();
        }
      },
      immediate: true,
      deep: true,
    },
    pageShow: {
      handler(newVal, oldVal) {
        if (newVal !== oldVal) {
          this.checkPollingTasks();
        }
      },
      immediate: true,
      deep: true,
    },
    'walletInfo.businessNo': {
      handler(newVal, oldVal) {
        console.log('walletInfo.businessNo', newVal, oldVal);
        if (newVal && newVal !== oldVal) {
          this.init();
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    async getWalletInfo() {
      try {
        const data = await this.$store.dispatch('getWalletInfo', {
          staffRole: 2,
          refresh: true,
        });
        console.log('getWalletInfo', JSON.stringify(data));
        if (data?.memberDocNo) {
          this.clearPollingTasks();
          this.checkPollingTasks();
        }
      } catch (err) {
        console.error(err);
      }
    },
    init() {
      this.clearPollingTasks();
      this.checkPollingTasks();
    },
    checkPollingTasks() {
      if (this.pageShow === true && this.tabBarValue === 'gift-code') {
        this.createPollingTasks();
      } else {
        this.clearPollingTasks();
      }
    },
    createPollingTasks() {
      if (!this.role?.businessNo) {
        return;
      }
      if (!this.walletInfo?.memberDocNo) {
        this.getWalletInfo();
        return;
      }
      this.createOrderInfoPollingTask();
      this.createQrCodePollingTask();
    },
    clearPollingTasks() {
      clearInterval(this.qrCodePollingTaskId);
      clearInterval(this.orderPollingTaskId);
      this.qrCodePollingTaskId = null;
      this.orderPollingTaskId = null;
      this.loadingCode = false;
    },
    async refreshCode() {
      if (!this.walletInfo?.memberDocNo) {
        this.getWalletInfo();
        return;
      }
      await this.getQrCode();
    },
    // 获取二维码
    async getQrCode() {
      try {
        if (this.loadingCode) return;
        this.loadingCode = true;
        let { success, data } = await uni.$petro.AccountPlugin.caller({
          bizType: 'generatePayCode', // 生成支付码
          data: {
            businessIdx: this.walletInfo?.serialNo,
            mainAccountType: 1, // 角色类型
          },
        });
        if (!success) return;
        this.qrCode = data?.qrcode;
        this.qrCodeInfo.code = this.qrCode;
        QRCode(this.qrCodeInfo, res => {
          console.log('QRCode', res);
          this.loadingCode = false;
          this.shouldRefreshCode = false;
        });
      } catch (err) {
        console.error(err);
        this.loadingCode = false;
      }
    },
    // 创建每隔60秒刷新二维码
    async createQrCodePollingTask() {
      console.log('createQrCodePollingTask', this.qrCodePollingTaskId);
      if (this.qrCodePollingTaskId) return;
      this.qrCodePollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
        await this.getQrCode();
        return false;
      }, 60 * 1000);
    },
    // 轮询获取待支付订单
    async createOrderInfoPollingTask() {
      console.log('createOrderInfoPollingTask', this.qrCodePollingTaskId);
      if (this.qrCodePollingTaskId) return;
      if (this.orderPollingTaskId) return;
      this.orderPollingTaskId = uni.$petro.Utils.createPollingTask(async () => {
        if (!this.qrCode) return false;
        try {
          const { success, data } = await uni.$petro.PayPlugin.caller(
            {
              bizType: 'getBusinessNoList',
              data: {
                parameters: JSON.stringify({
                  mainAccountNo: this.walletInfo?.mainAccountNo, // 主账户编号
                  unitMainAccountNo: this.walletInfo?.enterpriseAccountNo, // 单位主账户编号
                  enterpriseStaffNo: this.walletInfo?.staffNo, // 员工编号
                  petrolChinaNo: this.walletInfo?.memberDocNo, // 员工档案编号
                  businessNo: this.walletInfo?.businessNo, // 业务编号
                  extendField: '', // 风控
                }),
              },
            },
            { ignoreErrs: true },
          );
          if (!!success) {
            uni.$petro.route({ url: '/pages/gift-code/pages/gift-pay/gift-pay', params: data, type: 'navigateTo' }, true);
            return true;
          }
        } catch (err) {
          console.error(err);
        }
      }, 5000);
    },
    // 扫码
    async onScan() {
      console.log('onScan');
      const res = await uni.$petro.scan({
        type: 4,
      });
      if (res?.success && res?.type === 'other') {
        console.log('onScan', res?.data?.authInfo);
        uni.showModal({
          title: '温馨提示',
          content: '实人认证成功',
          showCancel: false,
        });
      }
    },
    // 禁用截屏
    async disableScreenCapture() {
      // 只在 Android 系统下执行
      if (this.osName !== 'android') return;

      try {
        await uni.$petro.Bridge.zyzx.setUserCaptureScreen({
          enable: false,
        });
        console.log('已禁用截屏');
      } catch (error) {
        console.error('禁用截屏失败:', error);
      }
    },
    // 恢复截屏
    async enableScreenCapture() {
      // 只在 Android 系统下执行
      if (this.osName !== 'android') return;

      try {
        await uni.$petro.Bridge.zyzx.setUserCaptureScreen({
          enable: true,
        });
        console.log('已恢复截屏功能');
      } catch (error) {
        console.error('恢复截屏功能失败:', error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-gift-code {}

.header {
  background-color: #fff;
}

.container {
  padding: 36rpx 0 212rpx;
}

// 二维码
.code {
  width: 408rpx;
  height: 442rpx;
  padding-top: 40rpx;
  margin: 246rpx auto 0;
  box-sizing: border-box;
  background: url('/static/bg-code.png') no-repeat center center;
  background-size: 408rpx 442rpx;
  position: relative;

  &-value {
    width: 320rpx;
    height: 320rpx;
    margin: 0 auto;
    background: #f5f5f5;
  }

  &-loading {
    position: absolute;
    margin: auto;
    z-index: 1;
    height: 50px;
    inset: 0;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
  }
}
</style>
