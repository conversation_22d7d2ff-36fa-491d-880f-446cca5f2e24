<template>
  <div class="page-gift-result">
    <u-navbar :title="'支付完成'" :autoBack="true" :placeholder="true"></u-navbar>

    <view class="result">
      <img src="@/static/icon-success.png" class="result-icon" />
      <view class="result-amount"> <span>￥</span>{{ resultInfo.payAmount || 0 }} </view>
      <view class="result-item">
        <view class="result-item-title">支付渠道</view>
        <view class="result-item-value">{{ resultInfo.channel }}</view>
      </view>
      <view class="result-item">
        <view class="result-item-title">支付时间</view>
        <view class="result-item-value">{{ resultInfo.transTime }}</view>
      </view>
    </view>

    <view class="footer">
      <button @click="onBack()">确认并返回</button>
    </view>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      resultInfo: {},
    };
  },
  computed: {},
  async onLoad(query) {
    this.resultInfo = query?.data ? JSON.parse(query?.data) || {} : {};
    this.resultInfo.channel = '中油智行APP';
    // uni.$emit('updateGiftCardCode', { msg: '更新礼品卡券支付二维码' });
  },
  methods: {
    onBack() {
      uni.$petro.route({
        type: 'back',
        delta: 1, // 默认是1，表示返回上一页，可不传delta
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-gift-result {
  min-height: 100vh;
  background: #ffffff;
}

.result {
  padding: 48rpx 32rpx 180rpx;
  box-sizing: border-box;

  &-icon {
    width: 96rpx;
    height: 96rpx;
    margin: 0 auto;
    display: block;
  }

  &-amount {
    margin-top: 42rpx;
    padding-bottom: 48rpx;
    box-shadow: 0px 1rpx 0px 0px #eeeeee;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 56rpx;
    color: #ff7033;
    line-height: 44rpx;
    text-align: center;

    & > span {
      font-size: 28rpx;
    }
  }

  &-item {
    margin-top: 32rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 44rpx;
    display: flex;
    align-items: center;

    &-title {
      color: #666666;
      width: 188rpx;
    }

    &-value {
      flex: 1;
      display: flex;
      align-items: center;

      & > span {
        font-family: PingFang SC, PingFang SC;
        font-weight: 400;
        font-size: 20rpx;
        color: #666666;
        width: auto;
        height: 32rpx;
        line-height: 28rpx;
        border: 2rpx solid #999999;
        border-radius: 8rpx;
        padding: 0 10rpx;
        box-sizing: border-box;
        margin-left: 12rpx;
      }
    }
  }
}

// 页面底部
.footer {
  width: 100%;
  height: 180rpx;
  padding: 28rpx 48rpx 60rpx;
  background: #ffffff;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;

  & > button {
    box-sizing: border-box;
    height: 92rpx;
    text-align: center;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    line-height: 92rpx;
    border-radius: 46rpx;
    border: none;
    background: #fa1919;
    color: #ffffff;
    flex: 1;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
