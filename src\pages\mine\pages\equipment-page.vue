<template>
  <div class="equipment-page">
    <u-navbar title="查看登录设备" :autoBack="true" :placeholder="true"></u-navbar>

    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-equipment></zyzx-page-equipment>
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import ZyzxPageEquipment from '@/components/zyzx-page-equipment/zyzx-page-equipment';

export default {
  name: 'equipment-page',
  // components: { ZyzxPageEquipment },
  data() {
    return {
      list: [],
    };
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.equipment-page {
  width: 100%;
  height: 100vh;
  padding: 24rpx 36rpx;
  box-sizing: border-box;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
