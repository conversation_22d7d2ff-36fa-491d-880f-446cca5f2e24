<template>
  <div>
    <view class="p-agreement">
      <view class="title">请阅读相关协议</view>
      <view class="agreement">
        <view class="agreement-content">
          <view>尊敬的客户您好：</view>
          <view>
            欢迎使用中油智行App！在您使用前，需要连接数据网络或者WLAN网络，产生的流量费用请咨询当地运营商。为了更好地保护您的权益，在使用本平台服务前，请您务必认真阅读并充分理解服务协议及隐私政策内容（简称"协议")。当您点击"同意"按钮，即表示您已完整理解并同意该协议内容。
          </view>
          <view>1. 保护用户隐私是本平台的一项基本政策，平台将依据相关法律法规及协议保护您的个人信息安全。</view>
          <view>2. 本平台将依据相关法律法规及协议，基于您的授权，向您申请和使用以下权限和信息：</view>
          <view class="permissions">储存权限 <view class="permissions-des">用于下载及缓存相关文件</view></view>
          <view class="permissions">位置权限 <view class="permissions-des">以便精准获取相关导航和加油服务</view></view>
          <view class="permissions">设备信息 <view class="permissions-des">用于推送和安全风控</view></view>
          <view class="permissions">消息通知权限 <view class="permissions-des">用于推送订单及权益等重要信息</view></view>
          <view class="permissions">相机相册权限 <view class="permissions-des">上传驾驶证等证件</view></view>
          <view>
            3. 个人相关信息和权限均不会默认或者强制开启收集信息。未经您的同意，本平台不会将您的信息共享给第三方或用于您未授权的其他用途。
          </view>
        </view>
      </view>
      <view class="block"></view>
      <view class="agreement-name">
        <view @click="onViewAgreement(2)">《用户服务协议》<u-icon name="arrow-right"></u-icon></view>
        <view @click="onViewAgreement(1)"> 《隐私政策》<u-icon name="arrow-right"></u-icon> </view>
      </view>
      <view class="footer">
        <button class="plain-btn" @click="tipShow = true">不同意</button>
        <button @click="onAgree()">同意</button>
      </view>

      <u-popup :show="tipShow" mode="bottom" :round="10" :closeOnClickOverlay="false">
        <view class="popup">
          <view class="popup-title">温馨提示</view>
          <view class="popup-content">
            本平台非常重视对您个人信息的保护，承诺严格按照本平台用户协议和隐私政策保护及处理您的信息。如不同意该协议，很遗憾我们将无法为您提供服务。
          </view>
          <view class="popup-footer">
            <button class="plain-btn" @click="onCancle()">退出应用</button>
            <button @click="onConfirm()">查看协议</button>
          </view>
        </view>
      </u-popup>
    </view>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 弹窗
      tipShow: false,
    };
  },
  async mounted() {},
  methods: {
    // 查看协议
    async onViewAgreement(value) {
      try {
        const res = await uni.$petro.http('user.agreement.h5', {
          agrmtType: value,
          regionCode: '510100',
        });
        console.log('res', res);
        if (!res?.data?.fileUrl) return uni.showToast({ content: '查看失败' });

        const res1 = await uni.$petro.downloadOpenFile(res.data.fileUrl);
        console.log('downloadOpenFile', res1);
      } catch (e) {}
    },
    // 同意
    async onAgree() {
      await uni.$petro.setTokenInfo({
        isAgreePrivacy: true,
      });
      this.$emit('agree');
    },
    // 查看协议
    onConfirm() {
      this.tipShow = false;
    },
    // 退出应用
    async onCancle() {
      this.tipShow = false;
      await uni.$petro.setTokenInfo({
        isAgreePrivacy: false,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.p-agreement {
  width: 100%;
  padding: 144rpx 0 210rpx;
  background: #ffffff;
  min-height: 100vh;
  box-sizing: border-box;
}

.title {
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  line-height: 44rpx;
  padding: 0 32rpx;
}

.agreement {
  &-content {
    width: 100%;
    padding: 32rpx;
    box-sizing: border-box;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 52rpx;

    & > view {
      margin-top: 16rpx;
    }

    & > view:first-child {
      margin-top: 0;
    }

    & > view:last-child {
      margin-top: 24rpx;
    }

    .permissions {
      font-weight: 600;
      color: #333333;
      line-height: 44rpx;
      display: flex;
      margin-top: 24rpx;

      &-des {
        font-weight: 400;
        color: #666666;
        margin-left: 32rpx;
      }
    }
  }

  &-name {
    width: 100%;
    padding: 0 32rpx;
    box-sizing: border-box;
    background: #ffffff;

    & > view {
      width: 100%;
      height: 100rpx;
      box-shadow: 0px 1rpx 0px 0px #eeeeee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-weight: 400;
      font-size: 28rpx;
      color: #fa1919;
    }
  }
}

.block {
  width: 100%;
  height: 24rpx;
  background: #f0f1f5;
}

// 按钮
button {
  box-sizing: border-box;
  height: 92rpx;
  text-align: center;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 32rpx;
  line-height: 92rpx;
  border-radius: 46rpx;
  border: none;
  background: #fa1919;
  color: #ffffff;
  flex: 1;

  &:active {
    opacity: 0.8;
  }
}

// 镂空按钮
.plain-btn {
  border: 1rpx solid #eeeeee;
  background: #ffffff;
  color: #333333;
}

// 页面底部
.footer {
  width: 100%;
  height: 180rpx;
  padding: 28rpx 48rpx 60rpx;
  background: #ffffff;
  box-sizing: border-box;
  position: fixed;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;

  & > button:nth-child(2) {
    margin-left: 16rpx;
  }
}

.popup {
  width: 100%;
  max-height: 76vh;
  padding: 0 40rpx;
  box-sizing: border-box;

  &-title {
    height: 50rpx;
    margin: 48rpx 0 40rpx;
    font-weight: 500;
    font-size: 36rpx;
    color: #333333;
    line-height: 50rpx;
    text-align: center;
  }

  &-content {
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 52rpx;
  }

  &-footer {
    padding: 52rpx 8rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > button:nth-child(2) {
      margin-left: 16rpx;
    }
  }
}
</style>
