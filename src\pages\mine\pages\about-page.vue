<template>
  <div class="about-page">
    <u-navbar title="关于我们" :autoBack="true" :placeholder="true"></u-navbar>

    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-about></zyzx-page-about>
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import ZyzxPageAbout from '@/components/zyzx-page-about/zyzx-page-about.vue';

export default {
  name: 'about-page',
  // components: { ZyzxPageAbout },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.about-page {
  width: 100%;
  height: 100vh;
  background: #f0f0f0;
  padding: 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
