<template>
  <div class="logout-page">
    <u-navbar :title="'账号注销'" :autoBack="true" :placeholder="true"></u-navbar>
    <div class="content">
      <div class="main-title">账户注销</div>
      <div class="description"> 你提交的注销申请生效前，将进行以下验证： </div>
      <div class="verification-list">
        <div class="verification-item" v-for="(item, index) in verificationItems" :key="index">
          <div class="number">{{ index + 1 }}</div>
          <div class="text">
            <div class="title">{{ item.title }}</div>
            <div class="subtitle">{{ item.subtitle }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="actions">
      <button class="btn-cancel" @click="onCancel">放弃注销</button>
      <button class="btn-confirm" @click="onConfirm">继续注销</button>
    </div>
  </div>
</template>

<script>
import { getBasicInfo, getStaffInfo, cancelUserApi } from '@/services/http/api.js';

export default {
  data() {
    return {
      verificationItems: [
        {
          title: '昆仑E享卡是否注销',
          subtitle: '该手机号绑定的昆仑E享卡是否注销',
        },
        {
          title: '无其他单位的昆仑E享卡',
          subtitle: '无其他正在使用中或有效的昆仑E享卡',
        },
      ],
      userInfo: {},
      authStatus: 1, // 身份校验状态:0-通过 1-短信验证 2-人脸验证
      messageType: 5, // 短信验证码类型:5-注销账号
      isConfirm: false, // 禁止点击
    };
  },
  onShow() {
    if (this.isConfirm) this.isConfirm = false;
  },
  async mounted() {
    try {
      this.getUserInfo();
    } catch (e) {
      console.log('mounted', e);
      this.isLoading = false;
    } finally {
      this.isLoading = false;
    }
  },
  onLoad(query) {
    console.log('loggon onLoad', query);
    try {
      if (query.data) query.data = JSON.parse(query.data);
      console.log('loggon-parse query.data', query.data);
      this.authStatus = query.data?.isOpened ? 2 : 1; // 身份校验：true走2-人脸验证，false走1-短信验证
    } catch (err) {
      console.error(err);
    }
  },
  methods: {
    onCancel() {
      console.log('放弃注销');
      // 可添加跳转或关闭逻辑
      uni.$petro.route({
        type: 'back',
        delta: 1, // 默认是1，表示返回上一页，可不传delta
      });
    },
    async onConfirm() {
      console.log('继续注销', this.userInfo);
      if (this.isConfirm) return;
      this.isConfirm = true;
      // 可添加跳转或提交逻辑
      this.toVerify();
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await uni.$petro.http(
          'user.getBasicInfo.h5',
          {
            type: 1, // 1-不脱敏信息 2-脱敏信息
          },
          { showLoading: false },
        );
        console.log('getUserInfo', res);
        this.userInfo = res?.data || {};
      } catch (e) {
        console.log(e);
      }
    },
    // 跳转验证页面
    async toVerify() {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/logout/identity-verification',
          params: {
            mobile: this.userInfo.phone,
            userId: this.userInfo.userId,
            messageType: this.messageType,
            tempCode: this.tempCode,
            authStatus: this.authStatus,
          },
          type: 'navigateTo',
        });
      }, 500);
    },
  },
};
</script>
<style scoped lang="scss">
.logout-page {
  background-color: #f9f9f9; /* 更柔和的背景色 */
  height: 100vh; /* 页面高度适应屏幕 */
  display: flex; /* 使用 flex 布局 */
  flex-direction: column; /* 垂直布局 */

  .content {
    flex: 1; /* 内容区域占据剩余空间 */
    padding: 24px;
    font-size: 14px;
    color: #333;

    .main-title {
      font-size: 24px; /* 大标题字体大小 */
      font-weight: bold; /* 加粗 */
      color: #333; /* 标题颜色 */
      margin-bottom: 16px; /* 与描述之间的间距 */
      text-align: center; /* 居中显示 */
    }

    .description {
      margin-bottom: 16px;
      font-size: 16px;
      color: #666;
      line-height: 1.5;
    }

    .verification-list {
      background-color: #fff; /* 白色背景 */
      border-radius: 8px; /* 圆角 */
      //box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
      padding: 16px;

      .verification-item {
        display: flex;
        align-items: flex-start;
        padding: 12px 0;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none; /* 去掉最后一项的边框 */
        }

        .number {
          width: 32px; /* 圆形宽度 */
          height: 32px; /* 圆形高度 */
          background-color: #2083ee; /* 淡蓝色背景 */
          color: #ffffff; /* 字体颜色白色 */
          font-size: 16px; /* 字体大小 */
          font-weight: bold; /* 字体加粗 */
          border-radius: 50%; /* 圆形 */
          display: flex; /* 使用 flex 布局 */
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */
          margin-right: 12px; /* 与文本的间距 */
        }

        .text {
          flex: 1;

          .title {
            font-size: 16px;
            color: #333;
            font-weight: 500;
          }

          .subtitle {
            font-size: 14px;
            color: #999;
            margin-top: 4px;
          }
        }
      }
    }
  }

  .actions {
    padding: 16px;
    display: flex;
    justify-content: space-between;
    background-color: #fff; /* 按钮区域背景色 */
    //box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); /* 阴影效果 */
    border-top: 1px solid #eee; /* 顶部边框 */

    .btn-cancel {
      flex: 1;
      margin-right: 12px;
      background-color: #fff;
      border: 1px solid #fa1919;
      border-radius: 24px;
      color: #fa1919;
      padding: 12px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      display: flex; /* 使用 flex 布局 */
      align-items: center; /* 垂直居中 */
      justify-content: center; /* 水平居中 */

      &:hover {
        background-color: #fa1919;
        color: #fff;
        transition: all 0.3s ease;
      }
    }

    .btn-confirm {
      flex: 1;
      margin-left: 12px;
      background-color: #fa1919;
      border: none;
      border-radius: 24px;
      color: #fff;
      padding: 12px;
      text-align: center;
      font-size: 16px;
      font-weight: bold;
      display: flex; /* 使用 flex 布局 */
      align-items: center; /* 垂直居中 */
      justify-content: center; /* 水平居中 */

      &:hover {
        background-color: #d01717;
        transition: all 0.3s ease;
      }
    }
  }
}
</style>
