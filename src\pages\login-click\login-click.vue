<template>
  <div class="page-login-click">
    <view class="container">
      <image class="logo" mode="widthFix" src="https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/login/logo.png"></image>
      <view class="title">欢迎登录中油智行~</view>
      <view class="phone">{{ phone }}</view>
      <view class="operators">{{ operators }}账户为您提供本机号码认证服务</view>
      <view class="agree" @click="isAgree = !isAgree">
        <image class="agree-checkbox" :src="`/static/icon-${isAgree ? 'agree' : 'disagree'}.png`"></image>
        <view class="agree-content">
          我已阅读并同意<span>《联通统一认证服务条款》</span>和<span>《服务协议》</span>、<span>《隐私政策》</span>
        </view>
      </view>
      <button @click="onLogin()">本机号码一键登录</button>
      <view class="methods" @click="onLoginPassword()">其他登录方式</view>
    </view>

    <u-modal
      :show="tipShow"
      confirmText="同意并继续"
      cancelText="不同意"
      :showCancelButton="true"
      @confirm="tipConfirm()"
      @cancle="tipShow = false"
    >
      <view class="slot-content">
        <view>为了更好的使用服务</view>
        <view>登录前请阅读并同意以下协议</view>
        <view>
          为保障您的信息安全，使用登录功能需要您先阅读并同意
          <span>《联通统一认证服务条款》</span>和<span>《服务协议》</span>、<span>《隐私政策》</span>
        </view>
      </view>
    </u-modal>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      phone: '156****5722', // 一键登录手机号
      operators: '联通', // 手机运营商
      isAgree: false, // 是否同意协议
      tipShow: false, // 是否显示提示弹窗
    };
  },
  computed: {},
  onLoad(query) {
    console.log('login onLoad', query);
    try {
      if (query.data) query.data = JSON.parse(query.data);
      console.log('parse query.data', query.data);
    } catch (err) {
      console.error(err);
    }
  },
  methods: {
    // 同意并继续
    tipConfirm() {
      uni.showToast({ content: '查看协议' });
    },
    // 其他登录方式，默认短信验证码登录
    onLoginPassword() {
      uni.$petro.route({ url: '/pages/login-code/login-code', type: 'redirectTo' });
    },
    // 登录
    onLogin() {
      uni.$petro.route({ url: '/pages/product/product', type: 'redirectTo' });
    },
  },
};
</script>

<style scoped lang="scss">
.page-login-click {
  background: #f5f5f5;
  padding: 180rpx 0 36rpx;
  text-align: center;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
}

.logo {
  width: 100px;
  margin: 12px auto;
  display: inherit;
}

.container {
  padding: 10px;
}

.agree {
  display: flex;
  text-align: left;
  margin-top: 60rpx;

  &-checkbox {
    width: 32rpx;
    height: 32rpx;
    margin-top: 4rpx;
    margin-right: 6rpx;
  }

  &-content {
    flex: 1;
    font-size: 24rpx;
    color: #999999;
    line-height: 33rpx;

    & > span {
      color: #e64f22;
    }
  }
}

button {
  height: 88rpx;
  line-height: 88rpx;
  background: #e64f22;
  border-radius: 16rpx;
  margin-top: 20rpx;
  font-size: 30rpx;
  color: #ffffff;
}

.methods {
  margin-top: 22rpx;
}
</style>
