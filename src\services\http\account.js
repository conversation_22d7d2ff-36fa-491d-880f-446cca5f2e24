/**
 * @description 钱包单位卡注销
 * @param data
 * @typeParam
 * {
 *   verifyNo: '', // 人脸认证信息
 *   enterpriseAccountNo: 111, // 单位主账户编号
 *   mainAccountNo: 222, // 司机主账户编号
 * }
 * @param config
 * @return {*}
 */
export function walletLogoff(data, config) {
  const mockData = {
    success: true,
    data: {},
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('account.manager.cancelUnitCard.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
