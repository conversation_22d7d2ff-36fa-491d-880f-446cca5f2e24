<template>
  <div class="page-container">
    <u-navbar :title="'订单'" :autoBack="false" :leftIconColor="'transparent'" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <div class="page-order">
          <div class="header">
            <div class="tab-list">
              <div
                class="tab-item"
                :class="{ active: tab.value === tabActive }"
                v-for="(tab, k) in tabList"
                :key="k"
                @click="tabActiveChange(tab)"
                >{{ tab.name }}
              </div>
            </div>
            <div class="filter-row">
              <div class="filter-row-left" @click="screenPopupOpen()">
                <image src="/static/images/icon-order-filter.png"></image>
                <span>筛选</span>
              </div>
            </div>
          </div>
          <div class="content">
            <zyzx-data-list
              ref="dataList"
              :safeAreaInsetBottom="true"
              :showEmpty="showEmpty"
              :emptyImage="require('@/static/images/empty-order-list.png')"
              emptyText="未查询到订单"
              :widthImg="180"
              :heightImg="120"
              @refreshPullDown="refreshPullDown"
              @scrolltolower="scrollToLower"
              :isLoad="!!(dataList && dataList.length)"
            >
              <div class="order-list">
                <div class="order-item" v-for="(item, i) in dataList" :key="i" @click="toDetail(item)">
                  <div class="detail-top">
                    <div class="name">
                      <div class="title text-overflow">{{ item.transactionPlaceName }}</div>
                      <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                    </div>
                    <div class="status">{{ GIFT_CARD_ORDER_STATUS[item.orderStatus] }}</div>
                  </div>
                  <div class="detail-content" v-for="(goodItem, idx) in item.giftCardItems" :key="idx">
                    <div class="detail-left">
                      <image class="detail-left-img" src="/static/images/gift-card-order-cover.png" />
                      <div class="order-name">
                        <div class="name">{{ goodItem.cardStyleName }}  </div>
                          <!-- 仅最后一个 goodItem 显示 type-lable -->
                        <div class="type-lable" v-if="idx === item.giftCardItems.length - 1">
                          <template v-if="item.cardTypeNo === 4">
                              <span class="color-block" style="background-color: #6BBE92; color: #FFFFFF;">昆仑E享卡</span> <!-- 柔和绿色 -->
                          </template>
                          <template v-else-if="item.cardTypeNo === 5">
                              <span class="color-block" style="background-color: #A3C9F1; color: #FFFFFF;">礼品券</span> <!-- 柔和蓝色 -->
                          </template>
                          <template v-else-if="item.cardTypeNo === 13">
                              <span class="color-block" style="background-color: #F7A899; color: #FFFFFF;">提货券</span> <!-- 柔和橙红色 -->
                          </template>
                          <template v-else-if="item.cardTypeNo === 12">
                              <span class="color-block" style="background-color: #F9D278; color: #FFFFFF;">福利卡</span> <!-- 柔和黄色 -->
                          </template>
                          <template v-else>
                              <span class="color-block" style="background-color: #C4C4C4; color: #FFFFFF;">{{ item.cardTypeNo }}</span> <!-- 柔和灰色 -->
                           </template>
                        </div>
                      </div>
                    </div>
                    <div class="detail-price">
                      <div class="unitPrice">&yen;{{ goodItem.faceValue }}</div>
                      <div class="litre">{{ 'x ' + goodItem.cardNum + '张' }}</div>
                    </div>
                  </div>
                  <div class="payment-row">
                    <div></div>
                    <div class="amount">
                      <div>实付总额：</div>
                      <div>&yen;</div>
                      <div>{{ item.realAmount || 0 }}</div>
                    </div>
                  </div>
                  <div class="time-row">
                    <div class="car-num">创建时间</div>
                    <div class="time">{{ item.createTime }}</div>
                  </div>
                </div>
              </div>
            </zyzx-data-list>

            <!-- 筛选弹窗 -->
            <u-popup :show="showScreenPopup" mode="bottom" bgColor="#fff" @close="screenPopupClose" :round="16">
              <view class="screen-popup">
                <div class="close-wrap">
                  <div></div>
                  <div>筛选</div>
                  <u-icon name="close" color="#333333" size="18" @click="screenPopupClose"></u-icon>
                </div>
                <div class="screen-date">消费时间</div>
                <div class="date-content">
                  <div
                    v-for="(item, index) in dateList"
                    :key="index"
                    class="date-item"
                    :class="{ active: dateActive === item.id }"
                    @click="handelIsdateActive(index, item.id)"
                    >{{ item.name }}
                  </div>
                </div>
                <div class="calendar-date">
                  <div class="picker">
                    <picker
                      mode="date"
                      :value="timeObj.startTime"
                      :start="timeRangeStart"
                      :end="timeRangeEnd"
                      @change="bindStartDateChange"
                      fields="day"
                    >
                      <div class="picker-content">
                        {{ timeObj.startTime }}
                        <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                      </div>
                    </picker>
                  </div>
                  <div class="line">-</div>
                  <div class="picker">
                    <picker
                      mode="date"
                      :value="timeObj.endTime"
                      :start="timeRangeStart"
                      :end="timeRangeEnd"
                      @change="bindEndDateChange"
                      fields="day"
                    >
                      <div class="picker-content">
                        {{ timeObj.endTime }}
                        <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                      </div>
                    </picker>
                  </div>
                </div>
                <div class="tips">可查询近2年内的消费订单</div>
                <div class="btn-row">
                  <button class="custom-btn-block red-plain circle" type="default" @click="dateReset()">重置</button>
                  <button class="custom-btn-block red circle" type="default" @click="dateConfirm()">确认</button>
                </div>
              </view>
            </u-popup>
          </div>
        </div>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { getGiftCardOrderListApi } from '@/services/http';

import { GIFT_CARD_ORDER_STATUS } from '@/services/enum';

export default {
  name: 'order',
  components: {},
  props: {},
  data() {
    return {
      GIFT_CARD_ORDER_STATUS: GIFT_CARD_ORDER_STATUS,
      showScreenPopup: false,
      tabActive: '',
      tabList: [
        {
          name: '全部',
          value: '',
        },
        // {
        //   name: '待支付',
        //   value: 1,
        // },
        {
          name: '已付款',
          value: 2,
        },
        {
          name: '已完成',
          value: 3,
        },
      ],
      pageNum: 1,
      pageSize: 10,
      dataList: [],
      showEmpty: false,
      // 时间绑定值
      dateActive: null,
      // 消费订单时间筛选按钮配置数组
      dateList: [
        { name: '近1个月', id: 1 },
        { name: '近3个月', id: 3 },
        { name: '近6个月', id: 6 },
        { name: '近1年', id: 12 },
      ],
      // 时间筛选范围绑定值
      timeObj: {
        startTime: '',
        endTime: '',
      },
      // 消费订单时间筛选范围限制开始时间
      timeRangeStart: '',
      // 消费订单时间筛选范围限制结束时间
      timeRangeEnd: '',
    };
  },
  computed: {
    ...mapState({
      role: state => state?.roles?.role,
      companyInfo: state => state?.company?.companyInfo,
      userInfo: state => state?.roles?.userInfo,
    }),
  },
  mounted() {
    // 执行初始化操作
    this.init();
  },
  methods: {
    // 初始化操作
    init() {
      this.dateReset();
      this.getList(true);
    },
    // 获取列表
    async getList(reload = false) {
      if (reload) {
        this.pageNum = 1;
        this.dataList = [];
        this.$refs.dataList.loadStatus = 'loading';
      }
      try {
        const params = {
          enterpriseNo: this.companyInfo?.enterpriseNo,
          orderStatus: this.tabActive,
          businessNo: this.role?.businessNo,
          beginTime: this.timeObj.startTime + ' 00:00:00',
          endTime: this.timeObj.endTime + ' 23:59:59',
          pageSize: this.pageSize,
          pageNum: this.pageNum,
        };

        const { success, data } = await getGiftCardOrderListApi(params);

        if (!success) return;
        const newList = data?.rows || [];

        // 当前为第一页则直接赋值, 否则合并新数据
        this.dataList = this.pageNum === 1 ? newList : this.dataList.concat(newList);
        // 当前页大于等于总页数,则没有更多了。禁用上拉加载更多
        this.$refs.dataList.loadStatus = this.pageNum >= data.pageSum ? 'nomore' : 'contentdown';
        // 是否展示无数据标识
        this.showEmpty = this.dataList.length === 0;
        this.pageNum++;
      } catch (error) {
        console.log(error);
      }
    },
    // 处理时间筛选按钮和时间范围的交互，处理时间格式
    handelIsdateActive(index, number) {
      this.dateActive = number;
      let date = new Date();
      let year = date.getFullYear(); // 年
      let month = date.getMonth() + 1; // 月
      let day = date.getDate(); // 日
      this.timeObj.endTime = this.getDate();
      month = month - (number % 12);
      year = year - parseInt(number / 12);
      day = day + 1;
      let lastDay = new Date(year, month, 0).getDate();
      if (day > lastDay) {
        day = 1;
        month += 1;
      }
      if (month > 12) {
        month = 12 - month;
        year += 1;
      }
      if (month <= 0) {
        month = month + 12;
        year = year - 1;
      }
      if (month >= 0 && month <= 9) {
        month = '0' + month;
      }
      if (day >= 0 && day <= 9) {
        day = '0' + day;
      }
      this.timeObj.startTime = year + '-' + month + '-' + day;
    },
    // 获取并处理当前时间
    getDate(type) {
      const date = new Date();
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();

      if (type === 'start') {
        year = year - 2;
        day += 1;
      } else if (type === 'end') {
      }
      var lastDay = new Date(year, month, 0).getDate();
      if (day > lastDay) {
        day = lastDay;
        month += 1;
      }
      if (month > 12) {
        year + 1;
      }
      month = month > 9 ? month : '0' + month;
      day = day > 9 ? day : '0' + day;
      return `${year}-${month}-${day}`;
    },
    // 确认筛选
    dateConfirm() {
      this.getList(true);
      this.showScreenPopup = false;
    },
    // 重置筛选
    dateReset() {
      // 默认时间两年
      this.handelIsdateActive(null, 24);
      // 初始化开始结束时间
      this.timeRangeStart = this.getDate('start');
      this.timeRangeEnd = this.getDate('end');
    },
    // 日期选择开始时间改变事件，重置时间按钮重置
    bindStartDateChange(e) {
      this.timeObj.startTime = e.target.value.replace(/\//g, '-');
    },
    // 日期选择结束时间改变事件，重置时间按钮重置
    bindEndDateChange(e) {
      this.timeObj.endTime = e.target.value.replace(/\//g, '-');
    },
    // 打开筛选弹窗
    screenPopupOpen() {
      this.showScreenPopup = true;
    },
    // 关闭筛选弹窗
    screenPopupClose() {
      this.showScreenPopup = false;
    },
    // tab切换
    tabActiveChange(tab) {
      this.tabActive = tab.value;
      this.getList(true);
    },
    // 跳转详情
    toDetail(v) {
      uni.$petro.route({ url: '/pages/order/pages/order-detail/order-detail', type: 'navigateTo', params: v }, true);
    },
    // 下拉刷新触发
    async refreshPullDown() {
      console.log('下拉刷新触发....');
      this.$refs.dataList.loadStatus = 'loading';
      await this.getList(true);
      this.$refs.dataList.stopRefresh();
    },
    // 上拉加载更多
    scrollToLower() {
      console.log('上拉加载更多....');
      // 可加载状态
      if (this.$refs.dataList.loadStatus === 'contentdown') {
        this.$refs.dataList.loadStatus = 'loading';
        this.getList();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100%;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;

  .page-content {
    flex: 1;
    overflow: hidden;
  }
}

.page-order {
  background: #f7f7fb;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .header {
    display: flex;
    flex-direction: column;
    background: #ffffff;

    .tab-wrap {
      padding: 0 30rpx;
      display: flex;
      justify-content: center;
      border-bottom: 2rpx solid #eee;
    }

    .tab-list {
      display: flex;
      padding: 10rpx 80rpx 0;

      justify-content: space-between;
      border-bottom: 2rpx solid #eee;

      .tab-item {
        height: 70rpx;
        line-height: 70rpx;
        font-size: 24rpx;
        color: #666666;
        padding-bottom: 4rpx;
        position: relative;

        &.active {
          font-weight: bold;
          transform: scale(1.05);
          font-size: 28rpx;
          color: #333333;

          &::after {
            content: '';
            width: 24rpx;
            height: 4rpx;
            background: #fa1919;
            border-radius: 4rpx;
            position: absolute;
            left: 50%;
            bottom: 0rpx;
            transform: translateX(-50%);
          }
        }
      }
    }

    .filter-row {
      padding: 0 30rpx;
      height: 106rpx;
      display: flex;
      justify-content: space-between;

      &-left {
        display: flex;
        align-items: center;

        &:active {
          opacity: 0.8;
        }

        image {
          width: 27rpx;
          height: 27rpx;
        }

        span {
          margin-left: 8rpx;
          font-size: 24rpx;
          color: #333333;
        }
      }
    }
  }

  .content {
    flex: 1;
    overflow: hidden;

    .order-list {
      padding: 32rpx 32rpx 50rpx;

      .order-item {
        padding: 20rpx 30rpx;
        border-radius: 16rpx;
        background: #ffffff;

        &:not(:first-child) {
          margin-top: 24rpx;
        }

        .detail-top {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .name {
            display: flex;
            align-items: center;
            overflow: hidden;

            image {
              width: 33rpx;
              height: 32rpx;
              flex-shrink: 0;
            }

            .title {
              margin-left: 8rpx;
              margin-right: 10rpx;
              font-size: 28rpx;
              font-weight: bold;
              color: #333333;
              line-height: 40rpx;
            }
          }

          .status {
            margin-left: 40rpx;
            flex-shrink: 0;
            font-size: 24rpx;
            color: #fa1919;
          }
        }

        .detail-content {
          margin-top: 16rpx;

          display: flex;
          justify-content: space-between;

          .detail-left {
            margin-right: 20rpx;
            flex: 1;
            display: flex;

            .detail-left-text {
              width: 78rpx;
              height: 78rpx;
              background: #f0f0f0;
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              span:nth-child(1) {
                color: #333333;
                font-size: 32rpx;
                font-weight: bold;
              }

              span:nth-child(2) {
                color: #333333;
                font-size: 20rpx;
                line-height: 20rpx;
                margin-top: -20rpx;
              }
            }

            .detail-left-img {
              width: 78rpx;
              height: 78rpx;
              border-radius: 16rpx;
            }

            .order-name {
              flex: 1;
              padding-top: 4rpx;
              margin-left: 20rpx;
              display: flex;
              //flex-direction: row;
              justify-content: space-between;

              .name {
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
              }
              .type-lable {
                width: 80px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                font-weight: bold;
                color: #FFFFFF; /* 字体颜色白色 */
              }
              
              .color-block {
                width: 100%;
                height: 100%;
                text-align: center;
                line-height: 30px;
                border-radius: 7px; /* 去掉圆角，锐化角度 */
              }
              .type {
                margin-top: 6rpx;
                display: flex;
                margin-right: 12rpx;

                span {
                  padding: 0 12rpx;
                  height: 42rpx;
                  line-height: 42rpx;
                  font-size: 22rpx;
                  color: #fa1919;
                  background: #fff3f3;
                  border-radius: 8rpx;
                }
              }
            }
          }

          .detail-price {
            text-align: right;

            .unitPrice {
              margin-top: 4rpx;
              font-size: 28rpx;
              font-weight: 400;
              color: #333333;
              line-height: 40rpx;
            }

            .litre {
              font-size: 20rpx;
              font-weight: 400;
              color: #666666;
              line-height: 40rpx;
            }
          }
        }

        .time-row {
          height: 48rpx;
          font-weight: 400;
          line-height: 40rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12rpx;

          .car-num {
            font-size: 26rpx;
            color: #666;
            height: 48rpx;
            line-height: 48rpx;
          }

          .time {
            font-size: 26rpx;
            color: #333333;
            height: 48rpx;
            line-height: 48rpx;
          }
        }

        .payment-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 12rpx;

          .amount {
            display: flex;
            flex-direction: row;

            div {
              font-size: 28rpx;
              line-height: 44rpx;
            }

            div:nth-child(1) {
              color: #666666;
            }

            div:nth-child(2) {
              color: #fa1919;
              font-size: 24rpx;
              margin-top: 4rpx;
            }

            div:nth-child(3) {
              color: #fa1919;
              font-weight: bold;
              font-size: 32rpx;
            }
          }
        }

        .relevant-info {
          margin-top: 28rpx;

          .info-item {
            display: flex;
            align-items: center;
            justify-content: space-between;

            div {
              font-size: 26rpx;
              color: #999999;
              line-height: 42rpx;
            }
          }
        }

        .detail-bottom {
          margin-top: 12rpx;

          .payment-status {
            display: flex;
            justify-content: end;

            .time {
              display: flex;
              align-items: center;

              div {
                font-size: 24rpx;
              }

              div:nth-child(1) {
                color: #e64f22;
              }

              div:nth-child(2) {
                margin-left: 12rpx;
                color: #333;
              }
            }

            .btn {
              padding: 0 12rpx;
              margin-left: 20rpx;
              background: #e64f22;
              color: #ffffff;
              border-color: #e64f22;
              border-radius: 10rpx;
            }
          }
        }
      }

      .more {
        margin-top: 30rpx;
        font-size: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }

  .screen-popup {
    padding: 50rpx 32rpx 32rpx;

    .close-wrap {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;

      div {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        line-height: 46rpx;
      }
    }

    .screen-date {
      margin-top: 32rpx;
      height: 46rpx;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    .date-content {
      margin-top: 40rpx;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .date-item {
        flex: 1;
        margin-right: 24rpx;
        box-sizing: border-box;
        border: 2rpx solid #f5f6f7;
        height: 60rpx;
        background: #f5f6f7;
        border-radius: 16rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333333;

        &:nth-of-type(4n) {
          margin-right: 0;
        }
      }
    }

    .calendar-date {
      display: flex;
      align-items: center;
      margin-top: 34rpx;
      justify-content: space-between;

      .line {
        width: 60rpx;
        text-align: center;
      }

      .picker {
        width: calc((100% - 60rpx) / 2);
        height: 80rpx;
        background: #f5f6f7;
        border-radius: 16rpx;
        padding: 0 24rpx 0 34rpx;
        box-sizing: border-box;

        .picker-content {
          height: 80rpx;
          line-height: 80rpx;
          display: flex;
          justify-content: space-between;
          align-items: center;
          text-align: center;
          font-size: 26rpx;
          color: #333333;

          img {
            width: 24rpx;
            height: 24rpx;
          }
        }
      }
    }

    .tips {
      margin-top: 24rpx;
      font-size: 26rpx;
      color: #999999;
      line-height: 37rpx;
    }

    .order-category {
      margin-top: 32rpx;
      height: 46rpx;
      font-size: 32rpx;
      color: #333333;
      line-height: 46rpx;
    }

    .category-content {
      margin-top: 24rpx;
      display: flex;
      flex-wrap: wrap;

      .date-item {
        box-sizing: border-box;
        border: 2rpx solid #f7f7fb;
        width: 155rpx;
        height: 60rpx;
        background: #f7f7fb;
        border-radius: 16rpx;
        line-height: 60rpx;
        text-align: center;
        font-size: 26rpx;
        color: #333333;
        margin-right: 24rpx;
      }

      .width-data-item {
        width: 300rpx;
      }
    }

    .btn-row {
      // margin-top: 44rpx;
      margin-top: 132rpx;
      display: flex;
      justify-content: space-between;

      button {
        width: calc((100% - 24rpx) / 2);
      }
    }

    .active {
      border: 2rpx solid #fa1919 !important;
      background: #ffffff !important;
      color: #fa1919 !important;
    }
  }
}


</style>
