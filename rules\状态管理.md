---
description: 企业小程序前端状态管理规范
globs: *.vue,*.js,store/*.js
alwaysApply: true
---

# 前端状态管理规范

## 总体描述

本文档规定了企业小程序前端项目的状态管理规范，包括Vuex模块化设计、状态设计原则、数据流规范和异步处理方式等内容，确保项目状态管理的一致性、可维护性和可扩展性。

## 应用范围

本文档适用于企业小程序项目的所有前端开发人员，涵盖Vuex状态管理的设计、实现和使用等相关开发工作。

## 使用要求

开发人员在进行状态管理相关开发时必须严格遵循本文档的规范要求，确保状态管理的规范性和一致性。

## 规则1 Vuex模块化设计

### 1.1 Store目录结构

**按业务模块组织Store文件**

```
services/
└── store/
    ├── index.js                # Store入口文件
    ├── tabBar.js               # 导航栏状态
    ├── roles.js                # 角色相关状态
    ├── company.js              # 企业相关状态
    ├── account.js              # 账户相关状态
    └── test1.js                # 测试模块状态
```

### 1.2 Store入口文件规范

**index.js必须统一注册所有模块**

```javascript
import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

// 导入模块
import tabBar from '@/services/store/tabBar';
import roles from '@/services/store/roles';
import company from '@/services/store/company';
import account from '@/services/store/account';

// 创建Store实例
const store = new Vuex.Store({
  modules: {
    // 系统模块
    ...uni.$petro.store.store,
    
    // 业务模块
    tabBar,
    roles,
    company,
    account
  }
});

export default store;
```

### 1.3 模块命名规范

**模块名称必须使用驼峰命名法，文件名使用短横线命名法**

```javascript
// ✅ 正确示例
// services/store/tabBar.js
export default {
  namespaced: true,
  state: { /* ... */ }
};

// services/store/user-profile.js
export default {
  namespaced: true,
  state: { /* ... */ }
};

// ❌ 错误示例
// services/store/TabBar.js - 文件名首字母不应大写
// services/store/userProfile.js - 文件名应使用短横线命名
```

## 规则2 状态设计原则

### 2.1 模块基本结构

**每个模块必须包含以下基本结构**

```javascript
/**
 * 角色状态管理模块
 * @description 管理用户角色和权限相关状态
 */
export default {
  // 启用命名空间
  namespaced: true,
  
  // 状态定义
  state: {
    // 用户角色信息
    roleInfo: null,
    
    // 权限列表
    permissions: [],
    
    // 加载状态
    loading: {
      roleInfo: false,
      permissions: false
    },
    
    // 错误信息
    errors: {
      roleInfo: null,
      permissions: null
    }
  },
  
  // 同步状态变更
  mutations: {
    // 设置角色信息
    SET_ROLE_INFO(state, roleInfo) {
      state.roleInfo = roleInfo;
    },
    
    // 设置权限列表
    SET_PERMISSIONS(state, permissions) {
      state.permissions = permissions;
    },
    
    // 设置加载状态
    SET_LOADING(state, { key, value }) {
      state.loading[key] = value;
    },
    
    // 设置错误信息
    SET_ERROR(state, { key, error }) {
      state.errors[key] = error;
    },
    
    // 重置状态
    RESET_STATE(state) {
      state.roleInfo = null;
      state.permissions = [];
      state.loading = {
        roleInfo: false,
        permissions: false
      };
      state.errors = {
        roleInfo: null,
        permissions: null
      };
    }
  },
  
  // 异步操作
  actions: {
    // 获取角色信息
    async fetchRoleInfo({ commit }) {
      try {
        commit('SET_LOADING', { key: 'roleInfo', value: true });
        commit('SET_ERROR', { key: 'roleInfo', error: null });
        
        const { success, data, message } = await uni.$petro.http('user.getRoleInfo.h5', {});
        
        if (!success) {
          throw new Error(message || '获取角色信息失败');
        }
        
        commit('SET_ROLE_INFO', data);
        return { success, data };
      } catch (error) {
        console.error('获取角色信息失败:', error);
        commit('SET_ERROR', { key: 'roleInfo', error: error.message });
        throw error;
      } finally {
        commit('SET_LOADING', { key: 'roleInfo', value: false });
      }
    },
    
    // 获取权限列表
    async fetchPermissions({ commit }) {
      // 实现获取权限列表的逻辑
    },
    
    // 切换角色
    async switchRole({ commit, dispatch }, roleId) {
      // 实现切换角色的逻辑
    },
    
    // 重置模块状态
    resetState({ commit }) {
      commit('RESET_STATE');
    }
  },
  
  // 计算属性
  getters: {
    // 是否有管理员权限
    isAdmin: state => {
      return state.roleInfo?.role === 'admin';
    },
    
    // 是否有指定权限
    hasPermission: state => permission => {
      return state.permissions.includes(permission);
    },
    
    // 角色名称
    roleName: state => {
      return state.roleInfo?.roleName || '未知角色';
    }
  }
};
```

### 2.2 状态命名规范

**状态属性必须使用驼峰命名法，表达清晰的业务含义**

```javascript
// ✅ 正确示例
state: {
  // 用户信息
  userInfo: null,
  
  // 是否已登录
  isLoggedIn: false,
  
  // 订单列表
  orderList: [],
  
  // 分页信息
  pagination: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  
  // 加载状态（分类管理）
  loading: {
    userInfo: false,
    orderList: false
  },
  
  // 过滤条件
  filters: {
    keyword: '',
    status: '',
    dateRange: null
  }
}

// ❌ 错误示例
state: {
  // 命名不清晰
  info: null,
  
  // 使用下划线命名
  user_info: null,
  
  // 缩写不明确
  ord: [],
  
  // 命名不表达含义
  data: [],
  list: [],
  flag: false
}
```

### 2.3 Mutation命名规范

**Mutation必须使用大写下划线命名法，表达操作类型和对象**

```javascript
// ✅ 正确示例
mutations: {
  // 设置用户信息
  SET_USER_INFO(state, userInfo) {
    state.userInfo = userInfo;
  },
  
  // 添加订单
  ADD_ORDER(state, order) {
    state.orderList.push(order);
  },
  
  // 更新订单
  UPDATE_ORDER(state, { id, data }) {
    const index = state.orderList.findIndex(item => item.id === id);
    if (index !== -1) {
      state.orderList[index] = { ...state.orderList[index], ...data };
    }
  },
  
  // 删除订单
  DELETE_ORDER(state, id) {
    state.orderList = state.orderList.filter(item => item.id !== id);
  },
  
  // 设置加载状态
  SET_LOADING(state, { key, value }) {
    state.loading[key] = value;
  }
}

// ❌ 错误示例
mutations: {
  // 使用驼峰命名
  setUserInfo(state, userInfo) {
    state.userInfo = userInfo;
  },
  
  // 命名不清晰
  update(state, data) {
    state.orderList = data;
  },
  
  // 操作类型和对象不明确
  process(state, { type, data }) {
    if (type === 'add') {
      state.orderList.push(data);
    } else if (type === 'update') {
      // 更新逻辑
    }
  }
}
```

### 2.4 Action命名规范

**Action必须使用驼峰命名法，动词开头表示操作**

```javascript
// ✅ 正确示例
actions: {
  // 获取用户信息
  async fetchUserInfo({ commit }) {
    // 实现逻辑
  },
  
  // 更新用户信息
  async updateUserInfo({ commit }, userInfo) {
    // 实现逻辑
  },
  
  // 获取订单列表
  async fetchOrderList({ commit }, params) {
    // 实现逻辑
  },
  
  // 创建订单
  async createOrder({ commit }, orderData) {
    // 实现逻辑
  },
  
  // 删除订单
  async deleteOrder({ commit }, id) {
    // 实现逻辑
  }
}

// ❌ 错误示例
actions: {
  // 使用名词开头
  userInfo({ commit }) {
    // 实现逻辑
  },
  
  // 操作不明确
  process({ commit }, data) {
    // 实现逻辑
  },
  
  // 使用下划线命名
  fetch_orders({ commit }) {
    // 实现逻辑
  }
}
```

### 2.5 Getter命名规范

**Getter必须使用驼峰命名法，表达计算结果**

```javascript
// ✅ 正确示例
getters: {
  // 是否已登录
  isLoggedIn: state => !!state.userInfo,
  
  // 用户姓名
  userName: state => state.userInfo?.name || '未登录',
  
  // 订单总金额
  totalOrderAmount: state => {
    return state.orderList.reduce((sum, order) => sum + order.amount, 0);
  },
  
  // 活跃订单列表
  activeOrders: state => {
    return state.orderList.filter(order => order.status === 'active');
  },
  
  // 是否有指定权限
  hasPermission: state => permission => {
    return state.permissions.includes(permission);
  }
}

// ❌ 错误示例
getters: {
  // 使用下划线命名
  user_name: state => state.userInfo?.name,
  
  // 命名不表达计算结果
  orders: state => state.orderList.filter(order => order.status === 'active'),
  
  // 命名不清晰
  check: state => permission => state.permissions.includes(permission)
}
```

## 规则3 数据流规范

### 3.1 单向数据流

**严格遵循Vuex单向数据流原则**

```javascript
// ✅ 正确示例
// 组件中获取状态
export default {
  computed: {
    ...mapState('user', ['userInfo']),
    ...mapGetters('user', ['isLoggedIn', 'userName'])
  },
  
  methods: {
    ...mapActions('user', ['fetchUserInfo', 'updateUserInfo']),
    
    async handleUpdateProfile() {
      try {
        // 调用action修改状态
        await this.updateUserInfo({
          name: this.form.name,
          email: this.form.email
        });
        
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: error.message || '更新失败',
          icon: 'none'
        });
      }
    }
  }
};

// ❌ 错误示例
// 直接修改state
export default {
  computed: {
    userInfo() {
      return this.$store.state.user.userInfo;
    }
  },
  
  methods: {
    handleUpdateProfile() {
      // 直接修改state，违反单向数据流
      this.$store.state.user.userInfo = {
        ...this.$store.state.user.userInfo,
        name: this.form.name,
        email: this.form.email
      };
    }
  }
};
```

### 3.2 组件与Store交互规范

**使用mapState、mapGetters、mapMutations和mapActions辅助函数**

```javascript
<template>
  <view class="user-profile">
    <!-- 使用计算属性访问状态 -->
    <view class="user-name">{{ userName }}</view>
    
    <!-- 加载状态 -->
    <view v-if="loading.userInfo" class="loading">
      <u-loading mode="circle"></u-loading>
    </view>
    
    <!-- 表单 -->
    <view v-else class="form">
      <u-form :model="form" ref="form">
        <u-form-item label="姓名" prop="name">
          <u-input v-model="form.name" placeholder="请输入姓名" />
        </u-form-item>
        
        <u-form-item label="邮箱" prop="email">
          <u-input v-model="form.email" placeholder="请输入邮箱" />
        </u-form-item>
      </u-form>
      
      <!-- 提交按钮 -->
      <u-button type="primary" @click="handleSubmit">保存</u-button>
    </view>
  </view>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
  data() {
    return {
      form: {
        name: '',
        email: ''
      }
    };
  },
  
  computed: {
    // 映射状态
    ...mapState('user', {
      userInfo: state => state.userInfo,
      loading: state => state.loading
    }),
    
    // 映射getter
    ...mapGetters('user', ['userName', 'isAdmin']),
    
    // 自定义计算属性
    formChanged() {
      return this.form.name !== this.userInfo?.name || 
             this.form.email !== this.userInfo?.email;
    }
  },
  
  // 页面加载时获取数据
  onLoad() {
    this.initData();
  },
  
  methods: {
    // 映射actions
    ...mapActions('user', ['fetchUserInfo', 'updateUserInfo']),
    
    // 初始化数据
    async initData() {
      try {
        await this.fetchUserInfo();
        
        // 填充表单
        if (this.userInfo) {
          this.form.name = this.userInfo.name;
          this.form.email = this.userInfo.email;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        uni.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      }
    },
    
    // 提交表单
    async handleSubmit() {
      // 表单验证
      const valid = await this.$refs.form.validate();
      
      if (!valid) return;
      
      // 检查是否有变更
      if (!this.formChanged) {
        uni.showToast({
          title: '未做任何修改',
          icon: 'none'
        });
        return;
      }
      
      try {
        // 调用action更新用户信息
        await this.updateUserInfo(this.form);
        
        uni.showToast({
          title: '保存成功',
          icon: 'success'
        });
      } catch (error) {
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      }
    }
  }
};
</script>
```

### 3.3 模块间通信规范

**使用命名空间和rootState/rootGetters进行模块间通信**

```javascript
// ✅ 正确示例
// services/store/order.js
export default {
  namespaced: true,
  
  state: {
    orderList: []
  },
  
  actions: {
    // 创建订单，需要用户信息
    async createOrder({ commit, rootState, dispatch }, orderData) {
      try {
        // 获取用户信息
        const userInfo = rootState.user.userInfo;
        
        // 如果没有用户信息，先获取
        if (!userInfo) {
          await dispatch('user/fetchUserInfo', null, { root: true });
        }
        
        // 创建订单逻辑
        const { success, data } = await uni.$petro.http('order.create.h5', {
          ...orderData,
          userId: rootState.user.userInfo.id
        });
        
        if (success) {
          commit('ADD_ORDER', data);
          
          // 通知用户模块更新订单计数
          dispatch('user/updateOrderCount', null, { root: true });
        }
        
        return { success, data };
      } catch (error) {
        console.error('创建订单失败:', error);
        throw error;
      }
    }
  }
};

// services/store/user.js
export default {
  namespaced: true,
  
  state: {
    userInfo: null,
    orderCount: 0
  },
  
  mutations: {
    SET_USER_INFO(state, userInfo) {
      state.userInfo = userInfo;
    },
    
    SET_ORDER_COUNT(state, count) {
      state.orderCount = count;
    }
  },
  
  actions: {
    // 获取用户信息
    async fetchUserInfo({ commit }) {
      // 实现逻辑
    },
    
    // 更新订单计数
    async updateOrderCount({ commit }) {
      try {
        const { success, data } = await uni.$petro.http('user.getOrderCount.h5', {});
        
        if (success) {
          commit('SET_ORDER_COUNT', data.count);
        }
      } catch (error) {
        console.error('获取订单计数失败:', error);
      }
    }
  }
};
```

## 规则4 异步处理规范

### 4.1 Action中的异步操作

**统一使用async/await处理异步操作**

```javascript
// ✅ 正确示例
actions: {
  // 获取用户信息
  async fetchUserInfo({ commit }) {
    try {
      // 设置加载状态
      commit('SET_LOADING', { key: 'userInfo', value: true });
      commit('SET_ERROR', { key: 'userInfo', error: null });
      
      // 发起请求
      const { success, data, message } = await uni.$petro.http('user.getInfo.h5', {});
      
      if (!success) {
        throw new Error(message || '获取用户信息失败');
      }
      
      // 更新状态
      commit('SET_USER_INFO', data);
      
      return { success, data };
    } catch (error) {
      console.error('获取用户信息失败:', error);
      commit('SET_ERROR', { key: 'userInfo', error: error.message });
      throw error;
    } finally {
      // 无论成功失败都要重置加载状态
      commit('SET_LOADING', { key: 'userInfo', value: false });
    }
  }
}

// ❌ 错误示例
actions: {
  // 使用回调方式处理异步
  fetchUserInfo({ commit }) {
    commit('SET_LOADING', { key: 'userInfo', value: true });
    
    uni.$petro.http('user.getInfo.h5', {}, {
      success: (res) => {
        if (res.success) {
          commit('SET_USER_INFO', res.data);
        } else {
          commit('SET_ERROR', { key: 'userInfo', error: res.message });
        }
        commit('SET_LOADING', { key: 'userInfo', value: false });
      },
      fail: (error) => {
        commit('SET_ERROR', { key: 'userInfo', error: error.message });
        commit('SET_LOADING', { key: 'userInfo', value: false });
      }
    });
  }
}
```

### 4.2 并行请求处理

**使用Promise.all处理并行请求**

```javascript
// ✅ 正确示例
actions: {
  // 初始化首页数据
  async initHomeData({ commit, dispatch }) {
    try {
      commit('SET_LOADING', { key: 'homeData', value: true });
      
      // 并行请求多个接口
      const [userResult, noticeResult, bannerResult] = await Promise.all([
        dispatch('fetchUserInfo'),
        dispatch('fetchNotices'),
        dispatch('fetchBanners')
      ]);
      
      // 处理结果
      return {
        success: userResult.success && noticeResult.success && bannerResult.success,
        data: {
          user: userResult.data,
          notices: noticeResult.data,
          banners: bannerResult.data
        }
      };
    } catch (error) {
      console.error('初始化首页数据失败:', error);
      throw error;
    } finally {
      commit('SET_LOADING', { key: 'homeData', value: false });
    }
  },
  
  // 获取用户信息
  async fetchUserInfo({ commit }) {
    // 实现逻辑
  },
  
  // 获取通知列表
  async fetchNotices({ commit }) {
    // 实现逻辑
  },
  
  // 获取轮播图
  async fetchBanners({ commit }) {
    // 实现逻辑
  }
}
```

### 4.3 错误处理规范

**统一的错误处理机制**

```javascript
// ✅ 正确示例
actions: {
  // 获取订单列表
  async fetchOrderList({ commit }, params) {
    try {
      // 设置加载状态
      commit('SET_LOADING', { key: 'orderList', value: true });
      commit('SET_ERROR', { key: 'orderList', error: null });
      
      // 参数处理
      const queryParams = {
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 10,
        ...params
      };
      
      // 发起请求
      const { success, data, message } = await uni.$petro.http('order.getList.h5', queryParams);
      
      if (!success) {
        throw new Error(message || '获取订单列表失败');
      }
      
      // 更新状态
      commit('SET_ORDER_LIST', data.list || []);
      commit('SET_PAGINATION', {
        current: data.pageNum,
        pageSize: data.pageSize,
        total: data.total
      });
      
      return { success, data };
    } catch (error) {
      // 错误处理
      console.error('获取订单列表失败:', error);
      commit('SET_ERROR', { key: 'orderList', error: error.message });
      
      // 显示错误提示
      uni.showToast({
        title: error.message || '获取订单列表失败',
        icon: 'none'
      });
      
      throw error;
    } finally {
      // 重置加载状态
      commit('SET_LOADING', { key: 'orderList', value: false });
    }
  }
}
```

## 规则5 持久化状态规范

### 5.1 Token存储与刷新

**统一使用uni.$petro.setTokenInfo和uni.$petro.getTokenInfo管理Token**

```javascript
// services/store/auth.js
export default {
  namespaced: true,
  
  state: {
    token: null,
    refreshToken: null,
    tokenExpireTime: 0
  },
  
  mutations: {
    SET_TOKEN_INFO(state, { token, refreshToken, expireTime }) {
      state.token = token;
      state.refreshToken = refreshToken;
      state.tokenExpireTime = expireTime;
    },
    
    CLEAR_TOKEN_INFO(state) {
      state.token = null;
      state.refreshToken = null;
      state.tokenExpireTime = 0;
    }
  },
  
  actions: {
    // 登录
    async login({ commit }, { mobile, password }) {
      try {
        const { success, data, message } = await uni.$petro.http('user.login.h5', {
          mobile,
          password
        });
        
        if (!success) {
          throw new Error(message || '登录失败');
        }
        
        // 保存Token信息
        const tokenInfo = {
          token: data.token,
          refreshToken: data.refreshToken,
          expireTime: Date.now() + data.expiresIn * 1000
        };
        
        // 更新状态
        commit('SET_TOKEN_INFO', tokenInfo);
        
        // 持久化存储
        await uni.$petro.setTokenInfo(tokenInfo);
        
        return { success, data };
      } catch (error) {
        console.error('登录失败:', error);
        throw error;
      }
    },
    
    // 退出登录
    async logout({ commit }) {
      try {
        // 清除状态
        commit('CLEAR_TOKEN_INFO');
        
        // 清除持久化存储
        await uni.$petro.setTokenInfo(null);
        
        return { success: true };
      } catch (error) {
        console.error('退出登录失败:', error);
        throw error;
      }
    },
    
    // 刷新Token
    async refreshToken({ commit, state }) {
      try {
        // 检查是否有刷新Token
        if (!state.refreshToken) {
          throw new Error('没有刷新Token');
        }
        
        // 发起刷新请求
        const { success, data, message } = await uni.$petro.http('user.refreshToken.h5', {
          refreshToken: state.refreshToken
        });
        
        if (!success) {
          throw new Error(message || '刷新Token失败');
        }
        
        // 保存新Token信息
        const tokenInfo = {
          token: data.token,
          refreshToken: data.refreshToken || state.refreshToken,
          expireTime: Date.now() + data.expiresIn * 1000
        };
        
        // 更新状态
        commit('SET_TOKEN_INFO', tokenInfo);
        
        // 持久化存储
        await uni.$petro.setTokenInfo(tokenInfo);
        
        return { success, data };
      } catch (error) {
        console.error('刷新Token失败:', error);
        
        // 清除Token信息
        commit('CLEAR_TOKEN_INFO');
        await uni.$petro.setTokenInfo(null);
        
        throw error;
      }
    },
    
    // 初始化Token信息
    async initTokenInfo({ commit }) {
      try {
        // 从持久化存储获取Token信息
        const tokenInfo = await uni.$petro.getTokenInfo();
        
        if (tokenInfo && tokenInfo.token) {
          // 更新状态
          commit('SET_TOKEN_INFO', tokenInfo);
          
          // 检查Token是否过期
          if (tokenInfo.expireTime && tokenInfo.expireTime < Date.now()) {
            // Token已过期，尝试刷新
            return await this.dispatch('auth/refreshToken');
          }
        }
        
        return { success: !!tokenInfo?.token };
      } catch (error) {
        console.error('初始化Token信息失败:', error);
        return { success: false };
      }
    }
  },
  
  getters: {
    // 是否已登录
    isLoggedIn: state => !!state.token,
    
    // Token是否即将过期（10分钟内）
    isTokenExpiring: state => {
      if (!state.tokenExpireTime) return false;
      return state.tokenExpireTime - Date.now() < 10 * 60 * 1000;
    }
  }
};
```

### 5.2 用户偏好设置存储

**使用uni.setStorageSync和uni.getStorageSync存储用户偏好**

```javascript
// services/store/settings.js
export default {
  namespaced: true,
  
  state: {
    // 主题设置
    theme: 'light',
    
    // 字体大小
    fontSize: 'medium',
    
    // 通知设置
    notifications: {
      push: true,
      email: false,
      sms: true
    }
  },
  
  mutations: {
    // 设置主题
    SET_THEME(state, theme) {
      state.theme = theme;
      // 持久化存储
      uni.setStorageSync('settings_theme', theme);
    },
    
    // 设置字体大小
    SET_FONT_SIZE(state, fontSize) {
      state.fontSize = fontSize;
      // 持久化存储
      uni.setStorageSync('settings_fontSize', fontSize);
    },
    
    // 设置通知配置
    SET_NOTIFICATIONS(state, notifications) {
      state.notifications = { ...state.notifications, ...notifications };
      // 持久化存储
      uni.setStorageSync('settings_notifications', state.notifications);
    }
  },
  
  actions: {
    // 初始化设置
    initSettings({ commit }) {
      try {
        // 从持久化存储获取设置
        const theme = uni.getStorageSync('settings_theme') || 'light';
        const fontSize = uni.getStorageSync('settings_fontSize') || 'medium';
        const notifications = uni.getStorageSync('settings_notifications') || {
          push: true,
          email: false,
          sms: true
        };
        
        // 更新状态
        commit('SET_THEME', theme);
        commit('SET_FONT_SIZE', fontSize);
        commit('SET_NOTIFICATIONS', notifications);
      } catch (error) {
        console.error('初始化设置失败:', error);
      }
    },
    
    // 更新主题
    updateTheme({ commit }, theme) {
      commit('SET_THEME', theme);
    },
    
    // 更新字体大小
    updateFontSize({ commit }, fontSize) {
      commit('SET_FONT_SIZE', fontSize);
    },
    
    // 更新通知设置
    updateNotifications({ commit }, notifications) {
      commit('SET_NOTIFICATIONS', notifications);
    }
  }
};
```

## 规则6 Vuex最佳实践

### 6.1 模块初始化

**在App.vue中统一初始化所有模块**

```javascript
// App.vue
export default {
  onLaunch: async function() {
    console.log('App Launch');
    
    // 初始化Token信息
    await this.$store.dispatch('auth/initTokenInfo');
    
    // 初始化用户设置
    this.$store.dispatch('settings/initSettings');
    
    // 如果已登录，获取用户信息
    if (this.$store.getters['auth/isLoggedIn']) {
      try {
        await this.$store.dispatch('user/fetchUserInfo');
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
    }
  },
  
  onShow: function() {
    console.log('App Show');
  },
  
  onHide: function() {
    console.log('App Hide');
  }
};
```

### 6.2 模块重置

**在退出登录时重置相关模块状态**

```javascript
// services/store/auth.js
actions: {
  // 退出登录
  async logout({ commit, dispatch }) {
    try {
      // 清除Token信息
      commit('CLEAR_TOKEN_INFO');
      await uni.$petro.setTokenInfo(null);
      
      // 重置相关模块状态
      dispatch('user/resetState', null, { root: true });
      dispatch('order/resetState', null, { root: true });
      
      return { success: true };
    } catch (error) {
      console.error('退出登录失败:', error);
      throw error;
    }
  }
}

// services/store/user.js
mutations: {
  // 重置状态
  RESET_STATE(state) {
    state.userInfo = null;
    state.permissions = [];
    state.loading = {
      userInfo: false,
      permissions: false
    };
    state.errors = {
      userInfo: null,
      permissions: null
    };
  }
},

actions: {
  // 重置模块状态
  resetState({ commit }) {
    commit('RESET_STATE');
  }
}
```

### 6.3 性能优化

**避免频繁修改状态和不必要的计算**

```javascript
// ✅ 正确示例
// 批量更新状态
mutations: {
  // 批量更新用户信息
  UPDATE_USER_INFO(state, userInfo) {
    state.userInfo = { ...state.userInfo, ...userInfo };
  }
},

actions: {
  // 批量更新用户信息
  async updateUserInfo({ commit }, userInfo) {
    try {
      const { success, data } = await uni.$petro.http('user.update.h5', userInfo);
      
      if (success) {
        // 一次性更新多个字段
        commit('UPDATE_USER_INFO', userInfo);
      }
      
      return { success, data };
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }
}

// ❌ 错误示例
// 频繁修改状态
mutations: {
  // 更新用户名
  UPDATE_USER_NAME(state, name) {
    state.userInfo = { ...state.userInfo, name };
  },
  
  // 更新邮箱
  UPDATE_USER_EMAIL(state, email) {
    state.userInfo = { ...state.userInfo, email };
  },
  
  // 更新手机号
  UPDATE_USER_MOBILE(state, mobile) {
    state.userInfo = { ...state.userInfo, mobile };
  }
},

actions: {
  // 更新用户信息
  async updateUserInfo({ commit }, { name, email, mobile }) {
    try {
      // 分别调用多个mutation
      if (name) commit('UPDATE_USER_NAME', name);
      if (email) commit('UPDATE_USER_EMAIL', email);
      if (mobile) commit('UPDATE_USER_MOBILE', mobile);
      
      // 发送请求
      return await uni.$petro.http('user.update.h5', { name, email, mobile });
    } catch (error) {
      console.error('更新用户信息失败:', error);
      throw error;
    }
  }
}
```

### 6.4 模块复用

**创建可复用的模块工厂函数**

```javascript
// services/store/factory/listModule.js
/**
 * 创建列表模块
 * @param {Object} options - 模块配置选项
 * @param {string} options.name - 模块名称
 * @param {string} options.apiPath - API路径
 * @param {Function} options.transformItem - 数据转换函数
 * @returns {Object} Vuex模块
 */
export function createListModule(options) {
  const { name, apiPath, transformItem = item => item } = options;
  
  return {
    namespaced: true,
    
    state: {
      list: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0
      },
      loading: false,
      error: null,
      filters: {}
    },
    
    mutations: {
      SET_LIST(state, list) {
        state.list = list.map(transformItem);
      },
      
      SET_PAGINATION(state, pagination) {
        state.pagination = { ...state.pagination, ...pagination };
      },
      
      SET_LOADING(state, loading) {
        state.loading = loading;
      },
      
      SET_ERROR(state, error) {
        state.error = error;
      },
      
      SET_FILTERS(state, filters) {
        state.filters = { ...state.filters, ...filters };
      },
      
      RESET_STATE(state) {
        state.list = [];
        state.pagination = {
          current: 1,
          pageSize: 10,
          total: 0
        };
        state.loading = false;
        state.error = null;
        state.filters = {};
      }
    },
    
    actions: {
      // 获取列表数据
      async fetchList({ commit, state }, params = {}) {
        try {
          commit('SET_LOADING', true);
          commit('SET_ERROR', null);
          
          // 合并参数
          const queryParams = {
            pageNum: params.pageNum || state.pagination.current,
            pageSize: params.pageSize || state.pagination.pageSize,
            ...state.filters,
            ...params
          };
          
          // 发起请求
          const { success, data, message } = await uni.$petro.http(apiPath, queryParams);
          
          if (!success) {
            throw new Error(message || `获取${name}列表失败`);
          }
          
          // 更新状态
          commit('SET_LIST', data.list || []);
          commit('SET_PAGINATION', {
            current: data.pageNum || queryParams.pageNum,
            pageSize: data.pageSize || queryParams.pageSize,
            total: data.total || 0
          });
          
          return { success, data };
        } catch (error) {
          console.error(`获取${name}列表失败:`, error);
          commit('SET_ERROR', error.message);
          throw error;
        } finally {
          commit('SET_LOADING', false);
        }
      },
      
      // 更新过滤条件
      updateFilters({ commit, dispatch }, filters) {
        commit('SET_FILTERS', filters);
        // 重置页码
        commit('SET_PAGINATION', { current: 1 });
        // 重新获取列表
        return dispatch('fetchList');
      },
      
      // 重置状态
      resetState({ commit }) {
        commit('RESET_STATE');
      }
    },
    
    getters: {
      // 是否有数据
      hasData: state => state.list.length > 0,
      
      // 是否正在加载
      isLoading: state => state.loading,
      
      // 是否有错误
      hasError: state => !!state.error,
      
      // 是否有更多数据
      hasMore: state => {
        return state.pagination.current * state.pagination.pageSize < state.pagination.total;
      }
    }
  };
}

// 使用示例
// services/store/order.js
import { createListModule } from './factory/listModule';

// 创建订单列表模块
const orderListModule = createListModule({
  name: '订单',
  apiPath: 'order.getList.h5',
  transformItem: item => ({
    ...item,
    statusText: getOrderStatusText(item.status),
    createTimeText: formatDate(item.createTime)
  })
});

// 扩展模块
export default {
  ...orderListModule,
  
  // 添加额外的状态
  state: {
    ...orderListModule.state,
    currentOrder: null
  },
  
  // 添加额外的mutations
  mutations: {
    ...orderListModule.mutations,
    
    SET_CURRENT_ORDER(state, order) {
      state.currentOrder = order;
    }
  },
  
  // 添加额外的actions
  actions: {
    ...orderListModule.actions,
    
    // 获取订单详情
    async fetchOrderDetail({ commit }, id) {
      try {
        commit('SET_LOADING', true);
        
        const { success, data, message } = await uni.$petro.http('order.getDetail.h5', { id });
        
        if (!success) {
          throw new Error(message || '获取订单详情失败');
        }
        
        // 转换数据
        const order = {
          ...data,
          statusText: getOrderStatusText(data.status),
          createTimeText: formatDate(data.createTime)
        };
        
        commit('SET_CURRENT_ORDER', order);
        
        return { success, data: order };
      } catch (error) {
        console.error('获取订单详情失败:', error);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  }
};
```

## 总结

本规范文档涵盖了企业小程序前端项目的状态管理规范，包括Vuex模块化设计、状态设计原则、数据流规范和异步处理方式等内容。开发人员必须严格遵循这些规范，确保状态管理的一致性、可维护性和可扩展性。

**重要提醒：**
1. 所有模块必须启用命名空间（namespaced: true）
2. 状态属性必须使用驼峰命名法，表达清晰的业务含义
3. Mutation必须使用大写下划线命名法，Action必须使用驼峰命名法
4. 严格遵循Vuex单向数据流原则，不直接修改state
5. 统一使用async/await处理异步操作
6. 实现统一的错误处理机制
7. 合理使用模块间通信和模块复用
