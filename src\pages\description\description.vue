<template>
  <div class="page-description">
    <petro-layout ref="layout">
      <zyzx-page-description />
    </petro-layout>
  </div>
</template>

<script>
// import ZyzxPageDescription from '@/components/zyzx-page-description/zyzx-page-description.vue';

export default {
  // components: { ZyzxPageDescription },
  data() {
    return {};
  },
  computed: {},
  onLoad() {},
  methods: {},
};
</script>

<style scoped lang="scss"></style>
