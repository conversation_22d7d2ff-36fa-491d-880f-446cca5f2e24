<template>
  <div class="page-webview">
    <petro-webview wvId="webview" :src="src" @message="onMessage" @onPostMessage="onPostMessage"></petro-webview>
  </div>
</template>

<script>
export default {
  created() {
    // this.webViewContext = my.createWebViewContext('webview');
    // this.webViewContext.postMessage({
    //   data: {
    //     name: '123',
    //   },
    // });
  },
  onLoad(query) {
    this.src = decodeURIComponent(query?.src || '');
    uni.setNavigationBarTitle({
      title: query?.title || '',
    });
  },
  data() {
    return {
      src: '',
      title: '',
    };
  },
  methods: {
    onMessage(e) {
      console.log('petro-webview onMessage:', e);
    },
    onPostMessage(e) {
      console.log('petro-webview onPostMessage:', e);
    },
  },
};
</script>

<style lang="scss" scoped></style>
