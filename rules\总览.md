---
description: 企业小程序前端项目开发总览
globs: *.vue,*.js,*.ts,*.scss,*.css
alwaysApply: true
---

# 前端开发总览

## 总体描述

本文档对面向toB的企业小程序前端项目（中油智行）进行了全面概述，涵盖技术栈选型、工程架构、开发规范等核心内容，为项目的整体架构和开发提供清晰的指引。

## 应用范围

本文档适用于企业小程序项目的所有前端开发人员、项目经理和相关技术人员，帮助他们了解项目的整体架构、技术选型和开发规范。

## 使用要求

开发人员在进行前端项目开发时必须严格遵循本文档及相关规则文档的要求进行开发，确保项目的一致性、可维护性和代码质量。项目经理可以根据本文档对前端项目进行整体规划和管理。

## 项目技术栈

### 核心框架

- **UniApp**: 跨平台应用开发框架，支持编译到微信小程序、支付宝小程序、H5等多个平台
- **Vue 2.x**: 渐进式JavaScript框架，作为UniApp的底层框架
- **JavaScript ES6+**: 使用现代JavaScript语法进行开发

### 状态管理

- **Vuex**: Vue.js的状态管理模式，集中式存储管理应用的所有组件状态
- **模块化设计**: 按业务模块划分store，包括以下核心模块：
  - **roles**: 角色和用户信息管理
  - **company**: 企业信息管理
  - **account**: 账户信息管理
  - **tabBar**: 底部导航栏状态管理
  - **其他业务模块**: 根据具体业务需求划分

### UI组件库

- **uView UI**: 基于UniApp的UI组件库，提供丰富的组件和主题定制
- **@petro-soti/foudation-zyzx**: 中油智行定制组件库，提供业务特定组件
- **自定义业务组件**: 项目特定的业务组件，如p-tabbar、p-login-password等

### 样式方案

- **SCSS**: CSS预处理器，支持变量、嵌套、混入等特性
- **uni.scss**: UniApp全局样式变量文件
- **响应式单位**: 使用rpx作为主要单位，适配不同屏幕尺寸
- **BEM命名法**: 组件样式采用Block-Element-Modifier命名规范

### 网络请求

- **uni.$petro.http**: 基于UniApp封装的HTTP请求库
- **Mock数据**: 开发阶段支持Mock数据，便于前端独立开发
- **拦截器**: 统一处理请求和响应，包括token添加、错误处理等

## 工程结构

```
petro-soti-zyzx-miniapp-user/
├── src/                           # 源代码目录
│   ├── components/                # 公共组件
│   │   ├── p-agreement/          # 协议组件
│   │   ├── p-login-password/     # 登录密码组件
│   │   ├── p-slider-verify/      # 滑块验证组件
│   │   ├── p-tabbar/             # 底部导航栏组件
│   │   ├── p-test/               # 测试组件
│   │   └── p-unit/               # 单元组件
│   ├── pages/                    # 页面文件
│   │   ├── index/                # 主页面（入口页）
│   │   ├── login/                # 登录相关页面
│   │   ├── login-click/          # 点击登录页面
│   │   ├── login-code/           # 验证码登录页面
│   │   ├── mine/                 # 我的页面
│   │   ├── product/              # 产品页面
│   │   ├── wallet/               # 钱包相关页面
│   │   ├── todo/                 # 待办页面
│   │   ├── todo-list/            # 待办列表页面
│   │   ├── gift-code/            # 礼品码页面
│   │   ├── identity-verification/# 身份验证页面
│   │   └── webview/              # Web视图页面
│   ├── services/                 # 业务服务层
│   │   ├── store/                # Vuex状态管理
│   │   │   ├── index.js          # store入口文件
│   │   │   ├── roles.js          # 角色相关状态
│   │   │   ├── company.js        # 企业相关状态
│   │   │   ├── account.js        # 账户相关状态
│   │   │   └── tabBar.js         # 导航栏状态
│   │   ├── http/                 # HTTP请求封装
│   │   │   ├── index.js          # 导出入口
│   │   │   ├── api.js            # 通用API接口
│   │   │   └── account.js        # 账户相关API
│   │   └── enum/                 # 枚举定义
│   ├── utils/                    # 工具函数
│   │   ├── mock.js               # Mock数据
│   │   └── format.js             # 格式化工具
│   ├── static/                   # 静态资源
│   ├── api/                      # API接口定义
│   ├── assets/                   # 资源文件
│   ├── config.global.js          # 全局配置
│   ├── main.js                   # 应用入口文件
│   ├── App.vue                   # 根组件
│   ├── pages.json                # 页面配置文件
│   ├── uni.scss                  # 全局样式变量
│   └── uni.promisify.adaptor.js  # Promise适配器
├── rules/                        # 开发规范文档
├── vue.config.js                 # Vue CLI配置
├── postcss.config.js             # PostCSS配置
└── package.json                  # 项目依赖配置
```

## 规范文档体系

| 序号 | 规则名称         | 用途描述                                                     |
| ---- | ---------------- | ------------------------------------------------------------ |
| 1    | 总览.md          | **技术栈说明**: 详细说明UniApp+Vue2+Vuex技术栈<br />**工程结构**: 展示企业小程序的完整目录组织<br />**开发环境**: 规范开发工具和环境配置 |
| 2    | 公共规则.md      | **命名规范**: 文件、组件、变量的命名约定<br />**代码风格**: JavaScript、Vue、SCSS代码规范<br />**注释规范**: 代码注释的格式和要求<br />**工具配置**: ESLint、Prettier等工具配置 |
| 3    | 路由.md          | **页面配置**: pages.json的配置规范<br />**路由跳转**: uni.$petro.route的使用规范<br />**页面生命周期**: UniApp页面生命周期的使用<br />**参数传递**: 页面间参数传递的标准方式 |
| 4    | 状态管理.md      | **Vuex模块化**: store模块的划分和组织方式<br />**状态设计**: state、mutations、actions、getters的设计原则<br />**数据流**: 组件与store的交互规范<br />**异步处理**: 异步操作的标准处理方式 |
| 5    | API接口.md       | **接口封装**: uni.$petro.http的使用规范<br />**Mock数据**: 开发阶段Mock数据的管理<br />**错误处理**: 统一的错误处理机制<br />**请求配置**: 请求参数和配置的标准格式 |
| 6    | UI.md            | **uView组件**: uView UI组件库的使用规范<br />**自定义组件**: 业务组件的开发规范<br />**样式规范**: SCSS变量、混入、响应式设计<br />**主题定制**: 组件主题和样式的定制方法<br />**交互规范**: 用户交互和反馈的设计标准 |
| 7    | 代码生成规范.md  | **组件生成**: 业务组件和通用组件的生成模板<br />**页面生成**: 标准页面的生成模板<br />**API接口生成**: 接口定义的生成模板<br />**Store模块生成**: 状态管理模块的生成模板<br />**工具函数生成**: 工具函数的生成模板 |

## 开发环境要求

### 必需工具

- **Node.js**: >= 14.x
- **HBuilderX**: 官方推荐IDE，支持UniApp开发调试
- **微信开发者工具**: 微信小程序调试
- **支付宝小程序开发者工具**: 支付宝小程序调试

### 推荐插件

- **Vue Language Features (Volar)**: Vue语法支持
- **SCSS IntelliSense**: SCSS语法支持
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

## 项目特色

### 多平台支持

- 支持编译到微信小程序、支付宝小程序、H5等多个平台
- 使用条件编译处理平台差异
- 统一的开发体验和代码复用

### 企业级架构

- 模块化的状态管理，支持复杂业务场景
- 完善的权限控制和角色管理
- 可扩展的组件体系和API封装

### 业务特色

- 用户身份验证和角色切换功能
- 多业务场景的统一入口
- 企业管理和个人用户的双重身份支持
- 完善的登录流程和安全验证机制

## 开发流程

### 1. 环境准备

1. 安装Node.js和npm/yarn
2. 安装HBuilderX和相关插件
3. 安装微信开发者工具和支付宝小程序开发者工具
4. 克隆项目代码并安装依赖：`yarn install`

### 2. 开发步骤

1. 根据需求分析，确定页面和组件结构
2. 创建页面和组件，遵循命名规范和目录结构
3. 开发业务逻辑，使用Vuex管理状态
4. 调用API接口，处理数据交互
5. 进行页面样式调整和交互优化

### 3. 调试与测试

1. 使用HBuilderX进行实时预览和调试
2. 在微信/支付宝开发者工具中进行平台适配测试
3. 进行功能测试和兼容性测试
4. 修复问题并优化性能

### 4. 构建与发布

1. 执行构建命令：`npm run build:mp-weixin` 或 `npm run build:mp-alipay`
2. 在开发者工具中上传代码
3. 提交审核并发布

## 最佳实践

1. **组件化开发**: 将UI和功能拆分为可复用的组件
2. **状态集中管理**: 使用Vuex管理应用状态，避免组件间直接传递复杂数据
3. **异步操作规范**: 统一使用async/await处理异步操作
4. **错误处理机制**: 建立完善的错误捕获和用户反馈机制
5. **条件编译**: 使用平台条件编译处理不同平台的差异
6. **性能优化**: 合理使用缓存、懒加载和按需加载
7. **代码审查**: 遵循团队代码规范，定期进行代码审查