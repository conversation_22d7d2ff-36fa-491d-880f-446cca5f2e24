import Vue from 'vue';
import Vuex from 'vuex';

Vue.use(Vuex);

import tabBar from '@/services/store/tabBar';
import test1 from '@/services/store/test1';
import test2 from '@/services/store/test2';
import account from '@/services/store/account';
import company from '@/services/store/company';
import roles from '@/services/store/roles';

const store = new Vuex.Store({
  modules: {
    ...uni.$petro.store.store,
    tabBar,
    test1,
    test2,
    account,
    company,
    roles,
  },
});

export default store;
