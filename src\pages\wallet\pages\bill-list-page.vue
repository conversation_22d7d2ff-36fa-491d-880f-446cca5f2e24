<template>
  <view class="bill-list-page">
    <u-navbar title="账单明细" :autoBack="true" :placeholder="true"></u-navbar>

    <!-- 账单列表 -->
    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-bill
          ref="zyzxPageBillRef"
          :enterpriseAccountNo="walletInfo.enterpriseAccountNo"
          :mainAccountNo="walletInfo.mainAccountNo"
          :isSortCategory="true"
        />
      </petro-layout>
    </div>
  </view>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'bill-list',
  data() {
    return {};
  },
  computed: {
    ...mapState({
      walletInfo: state => state?.account?.walletInfo,
    }),
  },
  onShow() {
    this.$nextTick(() => {
      this.$refs.zyzxPageBillRef.getBillList(true);
    });
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.bill-list-page {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
