<template>
  <view class="page-business-setting">
    <petro-layout ref="layout">
      <zyzx-page-todo-list :pageShow="pageShow"></zyzx-page-todo-list>
    </petro-layout>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      pageShow: false,
    };
  },
  onLoad(query) {
    console.log('onLoad', query);
  },
  onShow() {
    this.pageShow = !this.pageShow;
    console.log('onShow-pageShow', this.pageShow);
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
