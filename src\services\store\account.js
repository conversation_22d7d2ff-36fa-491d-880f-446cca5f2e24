export default {
  state: {
    walletInfo: {
      enterpriseAccountNo: '',
      mainAccountNo: '',
      enterpriseNo: '',
      userId: '',
      userName: '',
      licencePlate: '',
      unitAlias: '',
      accountType: 0,
      businessNo: '',
      businessType: 0,
      invoiceType: 0,
      accountStatus: 0,
      accountPlace: '',
      accountPlaceName: '',
      remark: '',
      serialNo: 0, // 420的businessIdx
      memberName: '',
      memberDocNo: '',
      enterpriseName: '',
      staffNo: '',
      useType: 1, // 资金使用类型 1分配 2共享
      isPassword: true,
      managePassword: true,
      loyaltyAccount: {
        pointAccountNo: '',
        frozenPointAmount: 0,
        availablePointAmount: 0,
        accountStatus: 0,
      },
      walletAccount: {
        cardNo: '',
        accountNo: '',
        frozenAmount: 0,
        availableAmount: 0,
        accountStatus: 0,
      },
    },
  },
  mutations: {
    setWalletInfo(state, value) {
      Object.assign(state.walletInfo, value);
      console.log(state.walletInfo);
    },
  },
  actions: {
    // 获取钱包信息
    async getWalletInfo({ commit, dispatch, state, rootState }, payload = {}) {
      const { accessToken, orgCode } = await dispatch('getTokenInfo', payload);
      if (!accessToken) {
        return console.info('用户未登录');
      }
      if (typeof payload === 'boolean') payload = { refresh: payload };
      if (!payload?.refresh && state.walletInfo?.staffNo) return state.walletInfo;
      try {
        // const params = {
        //   enterpriseNo: orgCode,
        //   businessNo: rootState?.roles?.role?.businessNo,
        //   staffRole: payload?.staffRole,
        // };
        console.log(rootState, 'rootState');
        console.log(rootState?.company, 'rootState?.company');

        const params = {
          businessNo: rootState?.roles?.role?.businessNo,
          enterpriseNo: orgCode,
          enterpriseAccountNo: rootState?.company?.accountInfo?.enterpriseAccountNo,
          mainAccountNo: rootState?.company?.accountInfo?.mainAccountNo,
        };
        let showLoading = true;
        if (typeof payload?.showLoading === 'boolean') showLoading = payload?.showLoading;
        // const { success, data } = await uni.$petro.http('account.manager.getDetailAccountInfo.h5', params, {
        //   showLoading: showLoading,
        // });

        const { success, data } = await uni.$petro.http('account.user.queryAccountDetailInfo.h5', params, {
          showLoading: showLoading,
          mockResponse: {
            success: true,
            data: {
              enterpriseAccountNo: '',
              mainAccountNo: '****************',
              enterpriseNo: '',
              userId: '',
              userName: '王二狗',
              licencePlate: '',
              unitAlias: '',
              accountType: 0,
              businessNo: '',
              businessType: 0,
              invoiceType: 1,
              accountStatus: 0,
              accountPlace: '',
              accountPlaceName: '重庆渝中',
              remark: '',
              serialNo: 0,
              memberName: '',
              memberDocNo: '',
              enterpriseName: '昆仑数智责任有限公司',
              staffNo: '',
              useType: 1, // 资金使用类型 1分配 2共享
              isPassword: false,
              managePassword: false,
              loyaltyAccount: {
                pointAccountNo: '',
                frozenPointAmount: 0.0,
                availablePointAmount: 99,
                accountStatus: 0,
              },
              walletAccount: {
                cardNo: '****************',
                accountNo: '',
                frozenAmount: 1,
                availableAmount: 9999999,
                accountStatus: 0,
              },
              fuelAccount: {
                promotionAccountNo: '',
                promotionFrozenAmount: 55,
                promotionAvailableAmount: 0.0,
                accountStatus: 0,
              },
            },
          },
        });
        if (success) {
          commit('setWalletInfo', data);
          return data;
        }
      } catch (error) {
        console.log(error);
      }
    },
  },
  getters: {},
};
