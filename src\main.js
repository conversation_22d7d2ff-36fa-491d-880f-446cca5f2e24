import Vue from 'vue';
import App from './App';
import './uni.promisify.adaptor';

Vue.config.productionTip = false;

App.mpType = 'app';

// #ifdef H5
import VConsole from 'vconsole';
const vConsole = new VConsole();
// vConsole.destroy();
// #endif

import uView from 'uview-ui';
Vue.use(uView);

// 插件工程 mPaaS小程序插件/支付宝小程序插件/微信小程序插件
// #ifdef MP-MPAAS-PLUGIN || MP-WX-PLUGIN || MP-ALI-PLUGIN
// 插件组件宿主小程序未使用，需要此处独立引入编译后才会有相应组件
import PTest from './components/p-test/p-test';
Vue.component('p-test', PTest);
// #endif

// 插件工程暂不支持公共依赖库
// #ifndef MP-MPAAS-PLUGIN || MP-WX-PLUGIN || MP-ALI-PLUGIN
import * as petro from '@petro-soti/foudation-zyzx';
import config from 'config.index.local';
import globalConfig from './config.global';
Vue.use(petro);
uni.$petro.init(uni.$u.deepMerge(config, globalConfig), store);
// #endif

import store from 'services/store';

const app = new Vue({
  ...App,
  store,
});
app.$mount();
