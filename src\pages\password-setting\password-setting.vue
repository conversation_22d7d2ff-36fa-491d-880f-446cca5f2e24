<template>
  <div class="page-password-setting">
    <view class="header">
      <image class="header-logo" src="@/static/icon-logo.png"></image>
      <!-- <view class="header-title">欢迎来到中油智行</view> -->
      <view class="header-value">
        <view class="header-title">欢迎来到中油智行</view>
        <view class="header-subTitle">智行在手&nbsp;便捷无忧</view>
      </view>
    </view>

    <view class="container">
      <view class="password">
        <input v-model="password" :password="!passwordVisable" />
        <view class="password-place" v-show="password.length === 0">
          请设置密码
          <view class="password-place-small">（8-16位数字、字母和符号组成）</view>
        </view>
        <img
          class="password-icon"
          :src="require(`@/static/icon-${!passwordVisable ? 'eyeClosed.png' : 'eye.png'}`)"
          @click="passwordVisable = !passwordVisable"
        />
      </view>
      <view class="password">
        <input v-model="confirmPassword" :password="!confirmPwdVisable" />
        <view class="password-place" v-show="confirmPassword.length === 0">
          再次输入密码
          <view class="password-place-small">（8-16位数字、字母和符号组成）</view>
        </view>
        <img
          class="password-icon"
          :src="require(`@/static/icon-${!confirmPwdVisable ? 'eyeClosed.png' : 'eye.png'}`)"
          @click="confirmPwdVisable = !confirmPwdVisable"
        />
      </view>
      <button @click="onConfirm()">确认</button>

      <view class="footer">
        <view class="footer-value" @click="onLogin()">登录</view>
        <view class="footer-line"></view>
        <view class="footer-value" @click="onMakePhone()">帮助</view>
      </view>
    </view>
  </div>
</template>

<script>
import { encrypt } from '@/utils';

export default {
  components: {},
  data() {
    return {
      actionType: '1', // 1-设置密码 2-忘记密码
      tempCode: '', // 临时码
      password: '', // 账号密码
      passwordVisable: false, // 密码是否可见
      confirmPassword: '', // 确认密码
      confirmPwdVisable: false, // 确认密码是否可见
    };
  },
  computed: {},
  onLoad(query) {
    console.log('password-setting', query);
    this.actionType = query.actionType || '1';
    this.tempCode = query.tempCode || '';
  },
  methods: {
    // 确认
    async onConfirm() {
      if (!this.verification()) return;

      try {
        /**
         * type 类型：1-设置密码 2-忘记密码
         * password 密码
         * tempCode 临时码
         */
        const res = await uni.$petro.http(
          'user.loginPassword.setOrForget.h5',
          {
            type: this.actionType,
            password: `ENC(${encrypt(this.password)})`,
            tempCode: this.tempCode,
          },
          { riskField: true },
        );
        console.log('res', res);

        if (!res?.success) {
          uni.showToast({ content: res.message || '设置密码失败' });
          return;
        }
        await uni.$petro.setTokenInfo({
          accessToken: '',
          expiresIn: '',
          gsmsToken: '',
          memberNo: '',
          orgCode: '',
        });
        uni.showToast({ content: '设置密码成功' });

        setTimeout(() => {
          uni.$petro.route({ url: '/pages/index/index', type: 'reLaunch' });
        }, 500);
      } catch (e) {
        // 临时码已过期
        if (e.errorCode === 'B_B15_505777' || e.errorCode === 'B_B15_100001') {
          setTimeout(() => {
            uni.$petro.route({
              type: 'back',
              delta: 1, // 默认是1，表示返回上一页，可不传delta
            });
          }, 500);
        }
      }
    },
    // 数据校验
    verification() {
      if (!/(?=.*\d)(?=.*[A-Za-z])(?=.*[^\w\s]).{8,16}$/.test(this.password)) {
        uni.showToast({ content: '请输入8-16位数字、字母和符号组成的密码' });
        return false;
      }

      if (this.confirmPassword !== this.password) {
        uni.showToast({ content: '两次输入的密码不一致' });
        return false;
      }

      return true;
    },
    // 登录
    onLogin() {
      uni.$petro.route({ url: '/pages/index/index', type: 'reLaunch' });
    },
    // 帮助 -- 跳转客服电话956100
    onMakePhone() {
      uni.makePhoneCall({
        phoneNumber: '956100',
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-password-setting {
  background: #ffffff;
  padding: 0 0 36rpx;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
}

.header {
  width: 100%;
  height: 480rpx;
  background: linear-gradient(53deg, rgba(121, 68, 255, 0.06) 0%, rgba(58, 145, 255, 0.15) 40%, rgba(41, 81, 154, 0.48) 100%);
  display: flex;
  align-items: flex-start;
  padding: 260rpx 32rpx 0;
  box-sizing: border-box;

  &-logo {
    width: 96rpx;
    height: 96rpx;
    margin-right: 32rpx;
  }

  &-value {
    flex: 1;
  }

  &-title {
    // flex: 1;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 62rpx;
  }

  &-subTitle {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 32rpx;
    margin-top: 4rpx;
  }
}

.container {
  padding: 32rpx;
}

input {
  width: 100%;
  height: 96rpx;
  padding: 0 32rpx;
  box-sizing: border-box;
  background: #f5f6f7;
  border-radius: 16rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 40rpx;
  color: #333333;
  line-height: 96rpx;
}

::v-deep .input-placeholder {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: #bfbfbf;
}

.password {
  position: relative;
  margin-top: 32rpx;
  height: 96rpx;

  &-icon {
    position: absolute;
    right: 36rpx;
    top: 28rpx;
    width: 40rpx;
    height: 40rpx;
  }

  &-place {
    position: absolute;
    left: 32rpx;
    top: 26rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #bfbfbf;
    line-height: 44rpx;
    display: flex;
    pointer-events: none;

    &-small {
      font-size: 24rpx;
    }
  }
}

button {
  height: 98rpx;
  background: #fa1919;
  border: none;
  border-radius: 49rpx;
  text-align: center;
  line-height: 98rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  letter-spacing: 18rpx;
  text-indent: 18rpx;
  margin: 48rpx 0 64rpx;

  &:active {
    opacity: 0.8;
  }
}

.methods {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  line-height: 32rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.footer {
  position: absolute;
  bottom: 96rpx;
  left: 0;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 40rpx;

  &-value {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #333333;
    line-height: 44px;
    margin: 0 48rpx;
  }

  &-line {
    width: 3rpx;
    height: 28rpx;
    background: #ccc;
  }
}
</style>
