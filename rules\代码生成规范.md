---
description: 企业小程序代码生成规范
globs: *.vue,*.js,*.ts,*.scss,*.css
alwaysApply: true
---

# 前端代码生成规范

## 总体描述

本文档规定了企业小程序的代码生成规范，包括组件生成、页面生成、API接口生成、Store模块生成等自动化代码生成的标准流程和模板规范，确保生成代码的一致性、可维护性和符合项目整体架构设计。

## 应用范围

本规范适用于企业小程序项目的所有自动化代码生成场景，包括新建组件、新建页面、新建API接口、新建Store模块等，确保生成的代码符合项目的整体规范和架构设计。

## 使用要求

开发人员在使用代码生成工具或手动创建新文件时，必须严格遵循本规范的模板结构、命名规范和文件组织要求。所有生成的代码必须符合项目的整体规范，包括命名规范、代码风格、注释规范等。

## 规则1 组件生成规范

### 1.1 业务组件生成模板

**业务组件必须使用p-前缀，并放置在独立目录中**

```bash
# 组件目录结构
components/
└── p-component-name/           # 组件目录（使用短横线命名）
    ├── p-component-name.vue    # 组件主文件
    ├── index.js                # 组件导出文件（可选）
    └── components/             # 子组件目录（可选）
        └── sub-component.vue   # 子组件
```

**组件模板结构**

```vue
<template>
  <view class="p-component-name">
    <!-- 组件内容 -->
    <view class="p-component-name__header">
      <text class="title">{{ title }}</text>
      <slot name="header-right"></slot>
    </view>
    
    <view class="p-component-name__content">
      <slot></slot>
    </view>
    
    <view class="p-component-name__footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
/**
 * 组件名称
 * @description 组件功能描述
 * @property {String} title - 标题
 * @property {Boolean} loading - 是否加载中
 * @event {Function} click - 点击事件
 * @example <p-component-name title="标题" @click="handleClick"></p-component-name>
 */
export default {
  name: 'PComponentName',
  
  props: {
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      // 组件内部数据
    };
  },
  
  computed: {
    // 计算属性
  },
  
  watch: {
    // 监听属性变化
  },
  
  created() {
    // 组件创建时执行
  },
  
  mounted() {
    // 组件挂载时执行
  },
  
  methods: {
    /**
     * 处理点击事件
     * @param {Event} event - 事件对象
     */
    handleClick(event) {
      this.$emit('click', event);
    }
  }
};
</script>

<style lang="scss" scoped>
.p-component-name {
  display: flex;
  flex-direction: column;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    
    .title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  &__content {
    flex: 1;
    padding: 24rpx;
  }
  
  &__footer {
    padding: 24rpx;
  }
}
</style>
```

### 1.2 通用组件生成模板

**通用组件不使用前缀，放置在独立目录中**

```bash
# 通用组件目录结构
components/
└── component-name/             # 组件目录（使用短横线命名）
    ├── component-name.vue      # 组件主文件
    ├── index.js                # 组件导出文件（可选）
    └── README.md               # 组件说明文档（推荐）
```

**通用组件模板结构**

```vue
<template>
  <view class="component-name">
    <!-- 组件内容 -->
  </view>
</template>

<script>
/**
 * 通用组件名称
 * @description 组件功能描述
 * @property {String} prop1 - 属性1说明
 * @property {Number} prop2 - 属性2说明
 * @event {Function} change - 值变化事件
 * @example <component-name prop1="value" :prop2="10" @change="handleChange"></component-name>
 */
export default {
  name: 'ComponentName',
  
  props: {
    // 属性1
    prop1: {
      type: String,
      default: ''
    },
    // 属性2
    prop2: {
      type: Number,
      default: 0,
      validator: value => value >= 0
    }
  },
  
  data() {
    return {
      // 组件内部数据
    };
  },
  
  methods: {
    /**
     * 触发变化事件
     * @param {*} value - 变化的值
     */
    emitChange(value) {
      this.$emit('change', value);
    }
  }
};
</script>

<style lang="scss" scoped>
.component-name {
  // 组件样式
}
</style>
```

## 规则2 页面生成规范

### 2.1 页面文件结构

**页面必须放置在独立目录中，包含主文件和组件**

```bash
# 页面目录结构
pages/
└── page-name/                  # 页面目录（使用短横线命名）
    ├── page-name.vue           # 页面主文件
    ├── components/             # 页面私有组件目录
    │   └── page-header.vue     # 页面私有组件
    └── modules/                # 页面模块目录（可选）
        └── data-list.js        # 数据处理模块
```

### 2.2 页面模板结构

**标准页面模板**

```vue
<template>
  <view class="page-name">
    <!-- 页面头部 -->
    <view class="page-name__header">
      <text class="page-title">{{ pageTitle }}</text>
    </view>
    
    <!-- 页面内容 -->
    <view class="page-name__content">
      <!-- 加载状态 -->
      <view class="loading" v-if="loading">
        <u-loading mode="circle"></u-loading>
      </view>
      
      <!-- 数据列表 -->
      <view class="data-list" v-else-if="dataList.length > 0">
        <view 
          class="data-item" 
          v-for="(item, index) in dataList" 
          :key="index"
          @click="handleItemClick(item)"
        >
          <text class="item-title">{{ item.title }}</text>
          <text class="item-desc">{{ item.description }}</text>
        </view>
      </view>
      
      <!-- 空状态 -->
      <view class="empty" v-else>
        <image class="empty-image" src="/static/empty.png"></image>
        <text class="empty-text">暂无数据</text>
      </view>
    </view>
    
    <!-- 页面底部 -->
    <view class="page-name__footer">
      <u-button type="primary" @click="handleAction">操作按钮</u-button>
    </view>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
  data() {
    return {
      pageTitle: '页面标题',
      loading: false,
      dataList: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        keyword: ''
      },
      hasMore: true
    };
  },
  
  computed: {
    ...mapState('module', ['stateData'])
  },
  
  onLoad(options) {
    // 接收页面参数
    this.initParams(options);
    // 初始化页面数据
    this.initPage();
  },
  
  onShow() {
    // 页面显示时刷新数据
    this.refreshData();
  },
  
  onPullDownRefresh() {
    // 下拉刷新
    this.handleRefresh();
  },
  
  onReachBottom() {
    // 上拉加载更多
    this.loadMore();
  },
  
  methods: {
    ...mapActions('module', ['fetchData']),
    
    /**
     * 初始化页面参数
     */
    initParams(options) {
      if (options.id) {
        this.id = options.id;
      }
      
      if (options.title) {
        this.pageTitle = options.title;
      }
    },
    
    /**
     * 初始化页面数据
     */
    async initPage() {
      try {
        this.loading = true;
        await this.fetchPageData();
      } catch (error) {
        console.error('初始化页面失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
        uni.stopPullDownRefresh();
      }
    },
    
    /**
     * 获取页面数据
     */
    async fetchPageData() {
      try {
        const { success, data } = await this.fetchData(this.queryParams);
        
        if (success) {
          this.dataList = data.list || [];
          this.hasMore = this.dataList.length < data.total;
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        throw error;
      }
    },
    
    /**
     * 刷新数据
     */
    refreshData() {
      this.queryParams.pageNum = 1;
      this.fetchPageData();
    },
    
    /**
     * 下拉刷新处理
     */
    handleRefresh() {
      this.refreshData().finally(() => {
        uni.stopPullDownRefresh();
      });
    },
    
    /**
     * 加载更多数据
     */
    loadMore() {
      if (!this.hasMore || this.loading) return;
      
      this.queryParams.pageNum += 1;
      this.fetchPageData();
    },
    
    /**
     * 处理列表项点击
     */
    handleItemClick(item) {
      uni.$petro.route({
        url: '/pages/detail/detail',
        type: 'navigateTo',
        params: {
          id: item.id
        }
      });
    },
    
    /**
     * 处理操作按钮点击
     */
    handleAction() {
      // 操作逻辑
    }
  }
};
</script>

<style lang="scss" scoped>
.page-name {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
  
  &__header {
    padding: 32rpx;
    background-color: #fff;
    
    .page-title {
      font-size: 36rpx;
      font-weight: 500;
      color: #333;
    }
  }
  
  &__content {
    flex: 1;
    padding: 24rpx;
    
    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 200rpx;
    }
    
    .data-list {
      .data-item {
        margin-bottom: 24rpx;
        padding: 24rpx;
        background-color: #fff;
        border-radius: 12rpx;
        
        .item-title {
          font-size: 32rpx;
          font-weight: 500;
          color: #333;
          margin-bottom: 12rpx;
        }
        
        .item-desc {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
    
    .empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 100rpx 0;
      
      .empty-image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 24rpx;
      }
      
      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
  
  &__footer {
    padding: 32rpx;
    background-color: #fff;
  }
}
</style>
```

## 规则3 API接口生成规范

### 3.1 API接口文件组织

**按业务模块组织API接口文件**

```bash
# API接口目录结构
services/
└── http/
    ├── index.js                # 统一导出入口
    ├── api.js                  # 通用业务接口
    ├── user.js                 # 用户相关接口
    ├── vehicle.js              # 车辆相关接口
    └── order.js                # 订单相关接口
```

### 3.2 API接口模板

**标准API接口定义模板**

```javascript
import mock from '@/utils/mock.js';

/**
 * @description 获取用户列表
 * @param {Object} data - 请求参数
 * @param {number} data.pageNum - 页码，从1开始
 * @param {number} data.pageSize - 每页数量
 * @param {string} data.keyword - 搜索关键词（可选）
 * @param {string} data.status - 用户状态（可选）
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回用户列表数据
 */
export function getUserList(data, config) {
  // 参数处理
  const params = {
    pageNum: Math.max(1, parseInt(data.pageNum) || 1),
    pageSize: Math.min(100, Math.max(1, parseInt(data.pageSize) || 10)),
    keyword: (data.keyword || '').trim(),
    status: data.status || ''
  };
  
  // 移除空值参数
  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });
  
  return uni.$petro.http('user.getList.h5', params, {
    showLoading: config?.showLoading !== false,
    ...config,
    mockResponse: mock.getUserList
  });
}

/**
 * @description 获取用户详情
 * @param {Object} data - 请求参数
 * @param {string} data.userId - 用户ID
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回用户详情数据
 */
export function getUserDetail(data, config) {
  // 参数验证
  if (!data.userId) {
    return Promise.reject(new Error('用户ID不能为空'));
  }
  
  return uni.$petro.http('user.getDetail.h5', data, {
    showLoading: true,
    ...config,
    mockResponse: mock.getUserDetail
  });
}

/**
 * @description 创建用户
 * @param {Object} data - 用户数据
 * @param {string} data.name - 用户姓名
 * @param {string} data.mobile - 手机号
 * @param {string} data.role - 角色
 * @param {Object} config - 请求配置
 * @returns {Promise<Object>} 返回创建结果
 */
export function createUser(data, config) {
  // 必填参数验证
  const requiredFields = ['name', 'mobile', 'role'];
  const missingFields = requiredFields.filter(field => !data[field]);
  
  if (missingFields.length > 0) {
    return Promise.reject(new Error(`缺少必填参数: ${missingFields.join(', ')}`));
  }
  
  // 数据格式验证
  if (!/^1[3-9]\d{9}$/.test(data.mobile)) {
    return Promise.reject(new Error('手机号格式不正确'));
  }
  
  return uni.$petro.http('user.create.h5', data, {
    showLoading: true,
    riskField: true,
    ...config,
    mockResponse: mock.createUser
  });
}
```

## 规则4 Store模块生成规范

### 4.1 Store模块文件组织

**按业务模块组织Store文件**

```bash
# Store模块目录结构
services/
└── store/
    ├── index.js                # 统一导出入口
    ├── user.js                 # 用户相关状态
    ├── vehicle.js              # 车辆相关状态
    └── order.js                # 订单相关状态
```

### 4.2 Store模块模板

**标准Store模块定义模板**

```javascript
/**
 * 模块名称
 * @description 模块功能描述
 */
export default {
  // 启用命名空间
  namespaced: true,
  
  // 状态定义
  state: {
    // 列表数据
    list: [],
    
    // 详情数据
    detail: {},
    
    // 加载状态
    loading: {
      list: false,
      detail: false,
      submit: false
    },
    
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 10,
      total: 0
    },
    
    // 过滤条件
    filters: {
      keyword: '',
      status: '',
      dateRange: {
        startDate: '',
        endDate: ''
      }
    },
    
    // 错误信息
    errors: {
      list: null,
      detail: null,
      submit: null
    }
  },
  
  // 同步状态变更
  mutations: {
    /**
     * 设置列表数据
     * @param {Object} state - 状态对象
     * @param {Array} list - 列表数据
     */
    setList(state, list) {
      state.list = list || [];
    },
    
    /**
     * 设置详情数据
     * @param {Object} state - 状态对象
     * @param {Object} detail - 详情数据
     */
    setDetail(state, detail) {
      state.detail = detail || {};
    },
    
    /**
     * 设置加载状态
     * @param {Object} state - 状态对象
     * @param {Object} payload - 载荷
     * @param {string} payload.type - 类型：list|detail|submit
     * @param {boolean} payload.loading - 加载状态
     */
    setLoading(state, { type, loading }) {
      state.loading[type] = loading;
    },
    
    /**
     * 设置分页信息
     * @param {Object} state - 状态对象
     * @param {Object} pagination - 分页信息
     */
    setPagination(state, pagination) {
      state.pagination = {
        ...state.pagination,
        ...pagination
      };
    },
    
    /**
     * 设置过滤条件
     * @param {Object} state - 状态对象
     * @param {Object} filters - 过滤条件
     */
    setFilters(state, filters) {
      state.filters = {
        ...state.filters,
        ...filters
      };
    },
    
    /**
     * 设置错误信息
     * @param {Object} state - 状态对象
     * @param {Object} payload - 载荷
     * @param {string} payload.type - 类型：list|detail|submit
     * @param {string} payload.error - 错误信息
     */
    setError(state, { type, error }) {
      state.errors[type] = error;
    },
    
    /**
     * 重置状态
     * @param {Object} state - 状态对象
     */
    resetState(state) {
      state.list = [];
      state.detail = {};
      state.pagination = {
        current: 1,
        pageSize: 10,
        total: 0
      };
      state.filters = {
        keyword: '',
        status: '',
        dateRange: {
          startDate: '',
          endDate: ''
        }
      };
      state.errors = {
        list: null,
        detail: null,
        submit: null
      };
    }
  },
  
  // 异步操作
  actions: {
    /**
     * 获取列表数据
     * @param {Object} context - 上下文
     * @param {Function} context.commit - 提交mutation
     * @param {Object} context.state - 状态
     * @param {Object} payload - 请求参数
     * @returns {Promise<Object>} 返回请求结果
     */
    async fetchList({ commit, state }, payload = {}) {
      try {
        // 设置加载状态
        commit('setLoading', { type: 'list', loading: true });
        commit('setError', { type: 'list', error: null });
        
        // 处理请求参数
        const params = {
          pageNum: payload.pageNum || state.pagination.current,
          pageSize: payload.pageSize || state.pagination.pageSize,
          ...state.filters,
          ...payload
        };
        
        // 发起请求
        const { success, data, message } = await uni.$petro.http(
          'module.getList.h5',
          params
        );
        
        if (!success) {
          throw new Error(message || '获取列表失败');
        }
        
        // 更新状态
        commit('setList', data.list || []);
        commit('setPagination', {
          current: data.pageNum || params.pageNum,
          pageSize: data.pageSize || params.pageSize,
          total: data.total || 0
        });
        
        return { success, data };
        
      } catch (error) {
        console.error('获取列表失败:', error);
        commit('setError', { type: 'list', error: error.message });
        throw error;
      } finally {
        commit('setLoading', { type: 'list', loading: false });
      }
    },
    
    /**
     * 获取详情数据
     * @param {Object} context - 上下文
     * @param {Function} context.commit - 提交mutation
     * @param {Object} payload - 请求参数
     * @returns {Promise<Object>} 返回请求结果
     */
    async fetchDetail({ commit }, payload) {
      try {
        // 参数验证
        if (!payload.id) {
          throw new Error('ID不能为空');
        }
        
        // 设置加载状态
        commit('setLoading', { type: 'detail', loading: true });
        commit('setError', { type: 'detail', error: null });
        
        // 发起请求
        const { success, data, message } = await uni.$petro.http(
          'module.getDetail.h5',
          { id: payload.id }
        );
        
        if (!success) {
          throw new Error(message || '获取详情失败');
        }
        
        // 更新状态
        commit('setDetail', data);
        
        return { success, data };
        
      } catch (error) {
        console.error('获取详情失败:', error);
        commit('setError', { type: 'detail', error: error.message });
        throw error;
      } finally {
        commit('setLoading', { type: 'detail', loading: false });
      }
    },
    
    /**
     * 提交数据
     * @param {Object} context - 上下文
     * @param {Function} context.commit - 提交mutation
     * @param {Object} payload - 请求参数
     * @returns {Promise<Object>} 返回请求结果
     */
    async submitData({ commit }, payload) {
      try {
        // 设置加载状态
        commit('setLoading', { type: 'submit', loading: true });
        commit('setError', { type: 'submit', error: null });
        
        // 发起请求
        const { success, data, message } = await uni.$petro.http(
          payload.id ? 'module.update.h5' : 'module.create.h5',
          payload
        );
        
        if (!success) {
          throw new Error(message || '提交失败');
        }
        
        return { success, data };
        
      } catch (error) {
        console.error('提交失败:', error);
        commit('setError', { type: 'submit', error: error.message });
        throw error;
      } finally {
        commit('setLoading', { type: 'submit', loading: false });
      }
    }
  },
  
  // 计算属性
  getters: {
    /**
     * 是否有数据
     * @param {Object} state - 状态对象
     * @returns {boolean} 是否有数据
     */
    hasData: state => state.list.length > 0,
    
    /**
     * 是否正在加载
     * @param {Object} state - 状态对象
     * @returns {boolean} 是否正在加载
     */
    isLoading: state => Object.values(state.loading).some(loading => loading),
    
    /**
     * 是否有错误
     * @param {Object} state - 状态对象
     * @returns {boolean} 是否有错误
     */
    hasError: state => Object.values(state.errors).some(error => error !== null),
    
    /**
     * 获取活跃状态的数据
     * @param {Object} state - 状态对象
     * @returns {Array} 活跃状态的数据
     */
    activeItems: state => state.list.filter(item => item.status === 'active')
  }
};
```

## 规则5 工具函数生成规范

### 5.1 工具函数文件组织

**按功能分类组织工具函数**

```bash
# 工具函数目录结构
utils/
├── index.js                    # 统一导出入口
├── date.js                     # 日期处理工具
├── format.js                   # 格式化工具
├── validate.js                 # 验证工具
└── storage.js                  # 存储工具
```

### 5.2 工具函数模板

**标准工具函数定义模板**

```javascript
/**
 * 日期格式化
 * @description 将日期对象格式化为指定格式的字符串
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @param {string} format - 格式化模板，默认 'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 * @example
 * formatDate(new Date(), 'YYYY-MM-DD HH:mm:ss') // => '2024-01-01 12:00:00'
 * formatDate(1640995200000, 'YYYY/MM/DD') // => '2022/01/01'
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
  if (!date) return '';
  
  // 转换为Date对象
  let dateObj;
  if (date instanceof Date) {
    dateObj = date;
  } else if (typeof date === 'string') {
    dateObj = new Date(date.replace(/-/g, '/'));
  } else if (typeof date === 'number') {
    dateObj = new Date(date);
  } else {
    return '';
  }
  
  // 判断日期是否有效
  if (isNaN(dateObj.getTime())) {
    return '';
  }
  
  const year = dateObj.getFullYear();
  const month = dateObj.getMonth() + 1;
  const day = dateObj.getDate();
  const hours = dateObj.getHours();
  const minutes = dateObj.getMinutes();
  const seconds = dateObj.getSeconds();
  
  // 格式化映射
  const map = {
    YYYY: year.toString(),
    MM: month.toString().padStart(2, '0'),
    DD: day.toString().padStart(2, '0'),
    HH: hours.toString().padStart(2, '0'),
    mm: minutes.toString().padStart(2, '0'),
    ss: seconds.toString().padStart(2, '0'),
    M: month.toString(),
    D: day.toString(),
    H: hours.toString(),
    m: minutes.toString(),
    s: seconds.toString()
  };
  
  // 替换格式
  return format.replace(/YYYY|MM|DD|HH|mm|ss|M|D|H|m|s/g, match => map[match]);
}

/**
 * 手机号脱敏
 * @description 将手机号中间4位替换为星号
 * @param {string} mobile - 手机号
 * @returns {string} 脱敏后的手机号
 * @example
 * maskMobile('13800138000') // => '138****8000'
 */
export function maskMobile(mobile) {
  if (!mobile || typeof mobile !== 'string') return '';
  return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 身份证号脱敏
 * @description 将身份证号中间部分替换为星号
 * @param {string} idCard - 身份证号
 * @returns {string} 脱敏后的身份证号
 * @example
 * maskIdCard('110101199001011234') // => '1101**********1234'
 */
export function maskIdCard(idCard) {
  if (!idCard || typeof idCard !== 'string') return '';
  return idCard.replace(/(\d{4})\d*(\d{4})/, '$1**********$2');
}
```

## 总结

本规范文档详细规定了企业小程序的代码生成规范，包括组件生成、页面生成、API接口生成、Store模块生成和工具函数生成等自动化代码生成的标准流程和模板规范。开发人员必须严格遵循这些规范，确保生成代码的一致性、可维护性和符合项目整体架构设计。

**重要提醒：**
1. 所有生成的代码必须符合项目的命名规范和代码风格
2. 业务组件必须使用p-前缀，并放置在独立目录中
3. 页面必须包含完整的生命周期处理和错误处理
4. API接口必须包含完整的JSDoc注释和参数验证
5. Store模块必须启用命名空间，并包含完整的状态管理结构
6. 工具函数必须包含完整的JSDoc注释和示例
