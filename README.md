# petro-soti-zyzx-miniapp-user

## 中油智行-会员小程序

## 环境

> 需要node环境16+

```shell
nvm use 16
npm install yarn -g
```

## 安装依赖

```shell
npm login --registry=https://************/repository/gsms-npm-public/
username: username
password: password

// OR 群内获取<NpmPublicToken>授权码
npm config set '//************/repository/gsms-npm-public/:_auth=<NpmPublicToken>'

// yarn 安装
yarn install
```

- PUPPETEER_SKIP_DOWNLOAD依赖安装异常

```shell
# for Windows (Command Prompt)
SET PUPPETEER_SKIP_DOWNLOAD=true
SET PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

# for Windows (Power Shell)
$env:PUPPETEER_SKIP_DOWNLOAD="true"
$env:PUPPETEER_SKIP_CHROMIUM_DOWNLOAD="true"

# for macOS and Linux
export PUPPETEER_SKIP_DOWNLOAD=true
export PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true
```

#### 更新联调公共库

```shell
yarn add @petro-soti/foudation-zyzx
```

#### dev/sit/pre/prd环境配置

```shell
vi src/config.index.local.js
```

#### 库API文档

```shell
cd node_modules/@petro-soti/foundation-zyzx
npm run opndocs
```

#### 开发

```shell
# for mPaas
npm run dev:mp-mpaas

# for alipay
npm run dev:mp-alipay

# for weixin
npm run dev:mp-weixin

# for alipay:sub
npm run dev:mp-alipay:sub

# for weixin:sub
npm run dev:mp-weixin:sub

# 自定义分包名
npm run dev:mp-alipay -- --subpackage=example
npm run dev:mp-weixin -- --subpackage=example

# for mpaas:plugin
npm run dev:mp-mpaas:plugin

# for alipay:plugin
npm run dev:mp-alipay:plugin

# for weixin:plugin
npm run dev:mp-weixin:plugin

# 自定义插件名
npm run dev:custom mp-mpaas-plugin -- --plugin plugin
npm run dev:custom mp-ali-plugin -- --plugin plugin
npm run dev:custom mp-wx-plugin -- --plugin plugin
```

#### 生产

```shell
# for mPaas
npm run build:mp-mpaas

# for alipay
npm run build:mp-alipay

# for weixin
npm run build:mp-weixin

# for alipay:sub
npm run build:mp-alipay:sub

# for weixin:sub
npm run build:mp-weixin:sub

# 自定义分包名
npm run build:mp-alipay -- --subpackage=example
npm run build:mp-weixin -- --subpackage=example

# for mpaas:plugin
npm run build:mp-mpaas:plugin

# for alipay:plugin
npm run build:mp-alipay:plugin

# for weixin:plugin
npm run build:mp-weixin:plugin

# 自定义插件名
npm run build:custom mp-mpaas-plugin -- --plugin plugin
npm run build:custom mp-ali-plugin -- --plugin plugin
npm run build:custom mp-wx-plugin -- --plugin plugin
```

## 通用开发规范

- IDE配置prettier格式化
- 文件夹/组件使用短横线命名
- 优先ES6语法，uview组件优先使用
- 禁用koroFileHeader注释说明
- git使用独立分支feature/feature-version-name开发，commitizen规范提交
- 保证质量，优雅代码，注释规范

## 前端开发规范

- 页面page定义：小写字母+短横线命名、如：other-page/other-page.vue
- 页面component定义：zyzx-page-name、如：zyzx-page-car-list/zyzx-page-car-list.vue
- 组件component定义：p-name、如：p-test/p-test.vue

## commitizen规范表

> 例子：feat(功能): 支持自定义主题

| 类型       | 描述                          |
|----------|-----------------------------|
| feat     | 新特性、新功能                     |
| fix      | 修改bug                       |
| ci       | 持续集成修改                      |
| perf     | 优化相关，比如提升性能、体验              |
| docs     | 文档修改                        |
| build    | 编译相关的修改，例如发布版本、对项目构建或者依赖的改动 |
| chore    | 其他修改，比如改变构建流程、或者增加依赖库、工具等   |
| refactor | 代码重构                        |
| revert   | 回滚到上一个版本                    |
| style    | 代码格式修改，注意不是 css 修改          |
| test     | 测试用例修改                      |

