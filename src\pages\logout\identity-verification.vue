<template>
  <div class="page-identity-verification">
    <u-navbar title="安全验证" :autoBack="true" :placeholder="true" :bgColor="'transparent'"> </u-navbar>

    <view class="black"></view>
    <view class="title" v-if="authStatus == 1">
      <view class="info">{{ mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</view>
      <view>我们将发送短信验证码到这个账号</view>
    </view>
    <view class="title" v-else>
      <view>请识别本人人脸，保持正对搜集，确保光线充足</view>
      <view class="info">{{ name || '' }}&nbsp;{{ identityNo || '' }}</view>
    </view>
    <img class="icon-face" :src="require(`@/static/icon-review-${authStatus == 1 ? 'code' : 'face'}.png`)" />
    <view class="tips">
      您正在注销该账号，为了您的账号安全，{{
        authStatus == 1 ? '将对您进行安全校验。我们将严格保护您的信息安全。' : '将对您进行人脸识别安全校验。我们将严格保护您的信息安全。'
      }}
    </view>

    <button v-if="!isPermission" @click="onGetMetaInfo()">开启相机</button>
    <button v-else :class="{ disabled: disabled }" :disabled="disabled" @click="handleVerify()">开始验证</button>

    <!-- 滑块验证码弹窗 -->
    <div v-if="sliderVisable">
      <p-slider-verify
        :show="sliderVisable"
        :mobile="mobile"
        :messageType="messageType"
        :tempCode="tempCode"
        @success="onSliderVerifySuccess"
        @close="onSliderClose"
      />
    </div>

    <root-portal :enable="true">
      <zyzx-permission :show="permissionShow" :permissionName="'camera'" @cancle="permissionShow = false"></zyzx-permission>
    </root-portal>
  </div>
</template>

<script>
import { decrypt } from '@/utils';
import { cancelUserApi } from '@/services/http/api.js';

export default {
  components: {},
  data() {
    return {
      sliderVisable: false, // 滑块验证是否显示
      mobile: '', // 手机号
      messageType: '4', // 1-用户注册 2-忘记密码 3-登录 4-换设备验证
      tempCode: '', // 临时验证码
      authStatus: 2, //  1-短信验证 2-人脸验证
      location: {}, // 位置信息
      disabled: true, // 是否禁用按钮
      authenticationInfo: null, // 初始化实人认证返回信息
      name: '', // 姓名
      identityNo: '', // 身份证号
      saveInfo: {}, // 存储信息
      isPermission: true, // 是否有相机权限
      permissionShow: false, // 是否显示权限弹窗
      authInfo: '', // 实人认证信息
      userInfo: {}, // 用户信息
      selectList: [], // 企业列表
    };
  },
  computed: {},
  async onLoad(query) {
    console.log('verify', query);
    this.messageType = query.messageType;
    this.mobile = query.mobile;
    this.tempCode = query.tempCode;
    this.authStatus = query.authStatus;
    if (query?.locationInfo) {
      this.location = JSON.parse(decodeURIComponent(query.locationInfo));
    }
    console.log('location', this.location);
    if (this.authStatus == 2) {
      this.onGetMetaInfo(true);
    } else {
      this.disabled = false;
    }
  },
  onShow() {
    if (!this.isPermission && this.authStatus == 2) {
      this.onGetMetaInfo();
    }
  },
  mounted() {
    try {
      this.getUserInfo();
      this.getUserEnterprise();
    } catch (e) {
      console.log('mounted', e);
      this.isLoading = false;
    } finally {
      this.isLoading = false;
    }
  },
  methods: {
    // 获取获取已、未开通企业列表
    async getUserEnterprise() {
      try {
        const res = await uni.$petro.http(
          'user.business.queryEnterpriseBusinessList.h5',
          {
            queryType: 5,
          },
          { showLoading: false },
        );
        console.log('res', res);
        this.selectList = res?.data || [];
      } catch (e) {
        this.isLoading = false;
      } finally {
        setTimeout(() => {
          this.isLoading = false;
        }, 800);
      }
    },
    // 开始验证
    handleVerify() {
      if (this.authStatus == 1) {
        // 滑块验证-短信验证
        this.sliderVisable = true;
      } else {
        // 人脸验证
        this.onGetMetaInfo();
      }
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisable = false;
      uni.showToast({ content: '短信验证码已发送' });
      this.toVerification();
    },
    // 滑块验证关闭
    onSliderClose(data) {
      this.sliderVisable = false;
      if (data?.isExpired) {
        setTimeout(() => {
          uni.$petro.route({
            type: 'back',
            delta: 1, // 默认是1，表示返回上一页，可不传delta
          });
        }, 500);
      }
    },
    // 跳转验证码页面
    toVerification() {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/logout/verification',
          params: {
            mobile: this.mobile,
            messageType: this.messageType,
            tempCode: this.tempCode,
            userId: this.userId,
            authStatus: this.authStatus,
            locationInfo: encodeURIComponent(JSON.stringify(this.location)),
          },
          type: 'redirectTo',
        });
      }, 500);
    },
    // 权限-获取权限
    getPermissions(isInit = false) {
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera'],
          showExplainAlert: isInit, // 解释弹窗蒙层
          checkNotReq: !isInit, // 不请求权限
        });
        resolve(res);
      });
    },
    // 获取人脸识别元信息
    async onGetMetaInfo(isInit = false) {
      let permission = await this.getPermissions(isInit);

      this.isPermission = !!permission?.camera;
      if (!this.isPermission) {
        !isInit && (this.permissionShow = true);
        return;
      }

      const res = await uni.$petro.Bridge.zyzx.faceAuth({
        bizType: 'getMetaInfo',
        data: {},
      });

      this.initAuthentication(res.data.metaInfo, isInit);
    },
    // 初始化人脸认证
    async initAuthentication(metaInfo) {
      /**
       * metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
       * areaCode 认证地市编码 例如：320100
       * roleBusinessList 业务角色列表
       *  - orgCode 单位编号
       *  - staffNo 员工编号
       *  - businessNo 业务编号
       *  - userRole 角色编码:1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
       *  - enterpriseAccountNo 单位账户编号 (司机人脸的时候必传)
       */
      let params = {
        metaInfo: metaInfo,
        areaCode: '510100',
        roleBusinessList: [],
      };

      this.selectList.forEach(item => {
        params.roleBusinessList.push({
          orgCode: item.enterpriseNo,
          staffNo: item.staffNo,
          businessNo: item.businessNo,
          userRole: item.role, // 车队业务管理员-2 司机-4
          enterpriseAccountNo: item.enterpriseAccountNo || item.parentMainAccountNo,
        });
      });
      try {
        const res = await uni.$petro.http('user.memberInfo.initRealPersonIdentify.h5', params);
        console.log('res', res);

        if (!res?.data) return uni.showToast({ content: res.message || '初始化失败' });

        this.authenticationInfo = res.data;
        this.faceVerify();
      } catch (e) {}
    },
    // 人脸识别
    async faceVerify() {
      try {
        // 获取人脸识别凭证
        const res = await uni.$petro.Bridge.zyzx.faceAuth({
          bizType: 'verify',
          data: {
            certifyId: this.authenticationInfo.certifyId,
          },
        });
        console.log('faceVerify', res);

        this.realPersonAuthentication();
      } catch (e) {}
    },
    // 实人认证
    async realPersonAuthentication() {
      try {
        /**
         * type 实人认证场景： 1—业务资料审核实人; 2—管理员激活企业; 3—司机首次注册; 4—扫一扫实人;5-账号注销
         * verifyNo 身份认证唯一标识(初始化实人认证接口返回)；
         * certifyId 实人认证三方系统的标识(初始化实人认证接口返回)。
         * roleBusinessList 业务角色列表
         *  - orgCode 单位编号
         *  - staffNo 员工编号
         *  - businessNo 业务编号
         *  - userRole 角色编码: 1-企业管理员 2-业务联系人 3-员工 4-司机 5-游客
         */
        const res = await uni.$petro.http('user.memberInfo.realPersonIdentify.h5', {
          type: '9',
          verifyNo: this.authenticationInfo.verifyNo,
          certifyId: this.authenticationInfo.certifyId,
          roleBusinessList: this.authenticationInfo.roleBusinessList,
        });
        console.log('res', res);
        if (!res?.success || !res?.data?.authInfo) return uni.showToast({ content: res.message || '实人认证失败' });

        this.logoutUser();
      } catch (e) {
        console.log('realPersonAuthentication error', e);
        uni.showToast({ content: e.message || '实人认证失败' });
      }
    },
    // 注销用户 V530
    async logoutUser() {
      let params = {
        authInfo: this.authInfo, //authInfo 步骤安全校验码
        authType: '9', // authType实人认证注销用户
      };
      console.log('identity-verification cancelUserApi params', params);
      // 调用注销用户接口
      const { success, data } = await cancelUserApi(params);
      console.log('identity-verification cancelUserApi', success, data);
      if (!success) return uni.showToast({ content: data?.message || '注销失败' });
      // 注销成功后，清除用户信息
      await uni.$petro.clearTokenInfo();
      setTimeout(() => {
        uni.$petro.route({
          type: 'reLaunch',
          url: '/pages/index/index',
        });
      }, 500);
    },
    // 获取用户信息
    async getUserInfo() {
      try {
        const res = await uni.$petro.http(
          'user.getBasicInfo.h5',
          {
            type: 1, // 1-不脱敏信息 2-脱敏信息
          },
          { showLoading: false },
        );
        console.log('getUserInfo', res);
        this.userInfo = res?.data || {};
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-identity-verification {
  background: #ffffff;
  padding-bottom: 36rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.black {
  height: 24rpx;
  background: #f0f1f5;
}

.title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 32rpx;
  margin-top: 64rpx;
  text-align: center;

  & > view:nth-child(2) {
    margin-top: 20rpx;
  }
}

.info {
  font-weight: 500;
  font-size: 40rpx;
  color: #333333;
  line-height: 56rpx;
}

.icon-face {
  width: 320rpx;
  height: 320rpx;
  margin: 72rpx auto;
  display: block;
}

.tips {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 28rpx;
  margin: 0 80rpx;
  text-align: center;
}

.tip-img {
  width: 100%;
  height: 150rpx;
  margin: 10rpx 0;
}

button {
  height: 98rpx;
  background: #fa1919;
  border: none;
  border-radius: 49rpx;
  text-align: center;
  line-height: 98rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  margin: 48rpx 32rpx 0;

  &:active {
    opacity: 0.8;
  }
}

.disabled {
  background: #ccc;
}
</style>
