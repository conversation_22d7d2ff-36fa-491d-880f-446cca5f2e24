{"name": "petro-soti-zyzx-miniapp-user", "version": "1.1.9", "release-version": "1.1.9", "private": true, "scripts": {"build:manifest": "node ./build/manifest/cli.js", "build:components": "node node_modules/@petro-soti/foudation-zyzx/bin/install.js", "postinstall": "npm run build:components && npm run build:manifest", "changelog": "conventional-changelog -p angular -u -i CHANGELOG.md -s -r 0", "serve": "npm run dev:h5", "build": "npm run build:h5", "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build", "build:custom": "cross-env NODE_ENV=production uniapp-cli custom", "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build", "build:mp-360": "cross-env NODE_ENV=production UNI_PLATFORM=mp-360 vue-cli-service uni-build", "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build --minimize", "build:mp-mpaas": "npm run build:custom mp-mpaas --minimize", "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build", "build:mp-jd": "cross-env NODE_ENV=production UNI_PLATFORM=mp-jd vue-cli-service uni-build", "build:mp-kuaishou": "cross-env NODE_ENV=production UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build", "build:mp-lark": "cross-env NODE_ENV=production UNI_PLATFORM=mp-lark vue-cli-service uni-build", "build:mp-qq": "cross-env NODE_ENV=production UNI_PLATFORM=mp-qq vue-cli-service uni-build", "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build", "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build --minimize", "build:mp-xhs": "cross-env NODE_ENV=production UNI_PLATFORM=mp-xhs vue-cli-service uni-build", "build:quickapp-native": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-native vue-cli-service uni-build", "build:quickapp-webview": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview vue-cli-service uni-build", "build:quickapp-webview-huawei": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build", "build:quickapp-webview-union": "cross-env NODE_ENV=production UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build", "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch", "dev:custom": "cross-env NODE_ENV=development uniapp-cli custom", "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve", "dev:mp-360": "cross-env NODE_ENV=development UNI_PLATFORM=mp-360 vue-cli-service uni-build --watch", "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch", "dev:mp-mpaas": "npm run dev:custom mp-mpaas", "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch", "dev:mp-jd": "cross-env NODE_ENV=development UNI_PLATFORM=mp-jd vue-cli-service uni-build --watch", "dev:mp-kuaishou": "cross-env NODE_ENV=development UNI_PLATFORM=mp-kuaishou vue-cli-service uni-build --watch", "dev:mp-lark": "cross-env NODE_ENV=development UNI_PLATFORM=mp-lark vue-cli-service uni-build --watch", "dev:mp-qq": "cross-env NODE_ENV=development UNI_PLATFORM=mp-qq vue-cli-service uni-build --watch", "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch", "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch", "dev:mp-xhs": "cross-env NODE_ENV=development UNI_PLATFORM=mp-xhs vue-cli-service uni-build --watch", "dev:quickapp-native": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-native vue-cli-service uni-build --watch", "dev:quickapp-webview": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview vue-cli-service uni-build --watch", "dev:quickapp-webview-huawei": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-huawei vue-cli-service uni-build --watch", "dev:quickapp-webview-union": "cross-env NODE_ENV=development UNI_PLATFORM=quickapp-webview-union vue-cli-service uni-build --watch", "info": "node node_modules/@dcloudio/vue-cli-plugin-uni/commands/info.js", "serve:quickapp-native": "node node_modules/@dcloudio/uni-quickapp-native/bin/serve.js", "test:android": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=android jest -i", "test:h5": "cross-env UNI_PLATFORM=h5 jest -i", "test:ios": "cross-env UNI_PLATFORM=app-plus UNI_OS_NAME=ios jest -i", "test:mp-baidu": "cross-env UNI_PLATFORM=mp-baidu jest -i", "test:mp-weixin": "cross-env UNI_PLATFORM=mp-weixin jest -i", "dev:mp-alipay:sub": "npm run dev:mp-alipay -- --subpackage=example", "dev:mp-weixin:sub": "npm run dev:mp-weixin -- --subpackage=example", "build:mp-alipay:sub": "npm run build:mp-alipay -- --subpackage=example", "build:mp-weixin:sub": "npm run build:mp-weixin -- --subpackage=example", "dev:mp-mpaas:plugin": "npm run dev:custom mp-mpaas-plugin -- --plugin plugin", "dev:mp-alipay:plugin": "npm run dev:custom mp-ali-plugin -- --plugin plugin", "dev:mp-weixin:plugin": "npm run dev:custom mp-wx-plugin -- --plugin plugin", "build:mp-mpaas:plugin": "npm run build:custom mp-mpaas-plugin -- --plugin plugin", "build:mp-alipay:plugin": "npm run build:custom mp-ali-plugin -- --plugin plugin", "build:mp-weixin:plugin": "npm run build:custom mp-wx-plugin -- --plugin plugin"}, "dependencies": {"@dcloudio/uni-app": "^2.0.2-3090820231124001", "@dcloudio/uni-app-plus": "^2.0.2-3090820231124001", "@dcloudio/uni-h5": "^2.0.2-3090820231124001", "@dcloudio/uni-i18n": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-360": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-alipay": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-baidu": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-jd": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-kuaishou": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-lark": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-qq": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-toutiao": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-vue": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-weixin": "^2.0.2-3090820231124001", "@dcloudio/uni-mp-xhs": "^2.0.2-3090820231124001", "@dcloudio/uni-quickapp-native": "^2.0.2-3090820231124001", "@dcloudio/uni-quickapp-webview": "^2.0.2-3090820231124001", "@dcloudio/uni-stacktracey": "^2.0.2-3090820231124001", "@dcloudio/uni-stat": "^2.0.2-3090820231124001", "@petro-soti/foudation-zyzx": "^1.2.0-1170", "@uni-ui/code-plugs": "^1.9.6", "@vue/shared": "^3.0.0", "commitizen": "^4.3.0", "core-js": "^3.8.3", "crypto-js": "^4.2.0", "flyio": "^0.6.2", "sass": "^1.70.0", "sass-loader": "10", "uview-ui": "2.0.36", "vconsole": "^3.15.1", "vue": ">= 2.6.14 < 2.7", "vuex": "^3.6.2"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/plugin-transform-runtime": "^7.24.0", "@babel/preset-env": "^7.24.0", "@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^2.0.2-3090820231124001", "@dcloudio/uni-cli-i18n": "^2.0.2-3090820231124001", "@dcloudio/uni-cli-shared": "^2.0.2-3090820231124001", "@dcloudio/uni-helper-json": "*", "@dcloudio/uni-migration": "^2.0.2-3090820231124001", "@dcloudio/uni-template-compiler": "^2.0.2-3090820231124001", "@dcloudio/vue-cli-plugin-hbuilderx": "^2.0.2-3090820231124001", "@dcloudio/vue-cli-plugin-uni": "^2.0.2-3090820231124001", "@dcloudio/vue-cli-plugin-uni-optimize": "^2.0.2-3090820231124001", "@dcloudio/webpack-uni-mp-loader": "^2.0.2-3090820231124001", "@dcloudio/webpack-uni-pages-loader": "^2.0.2-3090820231124001", "@vue/babel-preset-app": "^5.0.8", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-loader": "^9.1.3", "babel-plugin-import": "^1.11.0", "cross-env": "^7.0.2", "jest": "^25.4.0", "postcss-comment": "^2.0.0", "prettier": "2.8.8", "string-replace-loader": "^3.1.0", "vue-template-compiler": ">= 2.6.14 < 2.7"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog", "types": {"feat": {"description": "新特性、新功能", "title": "功能"}, "fix": {"description": "修改bug", "title": "修改"}, "ci": {"description": "持续集成修改", "title": "修改"}, "perf": {"description": "优化相关，比如提升性能、体验", "title": "性能"}, "docs": {"description": "文档修改", "title": "修改"}, "build": {"description": "编译相关的修改，例如发布版本、对项目构建或者依赖的改动", "title": "构建"}, "chore": {"description": "其他修改，比如改变构建流程、或者增加依赖库、工具等", "title": "修改"}, "refactor": {"description": "代码重构", "title": "重构"}, "revert": {"description": "回滚到上一个版本", "title": "回滚"}, "style": {"description": "代码格式修改，注意不是 css 修改", "title": "格式"}, "test": {"description": "测试用例修改", "title": "测试"}}}}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {"mp-mpaas": {"title": "m<PERSON><PERSON><PERSON>小程序", "BROWSER": "", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-MPAAS": true, "MP-ALIPAY": false}}, "mp-mpaas-plugin": {"title": "mPaaS小程序插件", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-MPAAS-PLUGIN": true}}, "mp-wx-plugin": {"title": "微信小程序插件", "env": {"UNI_PLATFORM": "mp-weixin"}, "define": {"MP-WX-PLUGIN": true}}, "mp-ali-plugin": {"title": "阿里小程序插件", "env": {"UNI_PLATFORM": "mp-alipay"}, "define": {"MP-ALI-PLUGIN": true}}}}}