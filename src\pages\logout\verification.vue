<template>
  <div class="page-verification">
    <view class="header">
      <image class="header-logo" src="@/static/icon-logo.png"></image>
      <!-- <view class="header-title">欢迎来到中油智行</view> -->
      <view class="header-value">
        <view class="header-title">欢迎来到中油智行</view>
        <view class="header-subTitle">智行在手&nbsp;便捷无忧</view>
      </view>
    </view>

    <view class="title">请输入短信验证码</view>
    <view class="subTitle">短信验证码发送至 {{ mobile }}</view>
    <view class="code">
      <u-code-input :adjustPosition="false" v-model="messageCode" :focus="codeFocus"></u-code-input>
    </view>

    <button @click="onInputFinish()">确认</button>

    <view class="time">
      <view v-show="isCountDown" class="time-countdown">
        <u-count-down ref="countDown" :time="time * 1000" format="ss" :autoStart="false" @finish="onFinish"> </u-count-down>
        秒后重新发送
      </view>
      <view v-show="!isCountDown" @click="sliderVisable = true">重新发送验证码</view>
      <view class="time-tips" @click="tipShow = true">没收到验证码？</view>
    </view>
    <u-modal :show="tipShow" confirmText="我知道了" @confirm="tipShow = false">
      <view class="slot-content">
        <view>收不到验证码</view>
        <view>1. 手机号码可能是为欠费停机状态，请及时充缴话费。</view>
        <view>2. 短信发送超过当天频次限制，国内短信24小时限制5次。</view>
        <view>3. 手机短信收件箱过满或手机内存不足，需清空后重启手机。</view>
        <view>4. 手机型号为双卡双待，请确认 AB 卡槽是否正确安放（可尝试更换 AB 卡位置重试）。</view>
        <view>5. 手机可能开启短信防骚扰功能。</view>
        <view>6. 如本人手机号码已停用，可通过账号找回更换手机号码。或联系您的客户服务网点，提供相关资质信息后更换手机号码。</view>
        <view>7. 如仍无法解决，请联系956100咨询客服。</view>
      </view>
    </u-modal>

    <!-- 滑块验证码弹窗 -->
    <div v-if="sliderVisable">
      <p-slider-verify
        :show="sliderVisable"
        :mobile="mobile"
        :messageType="messageType"
        :tempCode="tempCode"
        @success="onSliderVerifySuccess"
        @close="onSliderClose"
      />
    </div>
  </div>
</template>

<script>
import pSliderVerify from '@/components/p-slider-verify/p-slider-verify.vue';
import { cancelUserApi } from '@/services/http/api.js';

export default {
  components: { pSliderVerify },
  data() {
    return {
      messageType: '5', // 5-注销账号
      mobile: '', // 手机号
      tempCode: '', // 临时码
      location: {}, // 位置信息
      codeFocus: true, // 输入框是否聚焦
      messageCode: '', // 验证码
      messageCodeLength: 6, // 验证码长度
      time: 60, // 短信验证再次发送倒计时
      isCountDown: true, // 倒计时是否显示
      tipShow: false, // 短信验证提示
      isLoading: false, // 加载中
      sliderVisable: false, // 滑块验证弹窗
      saveInfo: {}, // 存储信息
      authInfo: '', // 安全校验码
    };
  },
  computed: {},
  onLoad(query) {
    console.log('verification', query);
    this.messageType = query.messageType;
    this.mobile = query.mobile;
    this.tempCode = query.tempCode;
    this.authstatus = query.authStatus; // 1-短信验证 2-人脸验证
    if (query?.locationInfo) {
      this.location = JSON.parse(decodeURIComponent(query.locationInfo));
    }
  },
  mounted() {
    this.$refs.countDown.start();
  },
  methods: {
    // 倒计时
    onFinish() {
      this.isCountDown = false;
      this.$refs.countDown.reset();
    },
    // 验证码输入完成
    async onInputFinish() {
      if (this.messageCode.length < 6) return uni.showToast({ content: '请输入6位验证码' });
      await this.checkCode();
    },
    // 校验验证码 -- 忘记密码
    async checkCode() {
      try {
        /**
         * mobile 手机号
         * type 验证码类型：（定义枚举类） 2-忘记密码（非登录态）； 3-提交入驻指导信息
         * messageCode 验证码
         */
        const res = await uni.$petro.http(
          'user.messageCode.check.h5',
          {
            mobile: this.mobile,
            messageCode: this.messageCode,
            type: '5', // 5-注销账号（短信模版）
            authType: 8, //短信验证注销用户
          },
          { riskField: true },
        );
        console.log('verification messageCode.check res', res);
        if (!res?.success) {
          this.isLoading = false;
          return uni.showToast({ content: res?.data?.message || '验证码校验失败' });
        }
        this.authInfo = res.data.authInfo;
        // 注销用户 V530 短信验证
        console.log('verification onInputFinish', this.messageCode, this.authInfo);
        this.logoutUser();
      } catch (e) {
        this.onReenter();
      }
    },
    // 注销用户 V530
    async logoutUser() {
      let params = {
        authInfo: this.authInfo,
        authType: '8', // 1-短信验证 2-人脸验证
      };
      console.log('verification logoutUser params', params);
      // 调用注销用户接口
      const { success, data } = await cancelUserApi(params);
      console.log('verification logoutUser res', success, data);
      if (!success) return uni.showToast({ content: data?.message || '注销失败' });

      uni.showToast({
        title: '提示',
        content: '注销成功',
        duration: 3000,
      });
      // 注销成功后，清除用户信息
      await uni.$petro.setTokenInfo({ gsmsToken: '', accessToken: '' });
      uni.$petro.route({ url: '/pages/index/index', type: 'reLaunch' });
    },
    // 重新输入验证码
    onReenter() {
      this.messageCode = '';
      this.isLoading = false;
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisable = false;
      uni.showToast({ content: '短信验证码已发送' });
      this.isCountDown = true;
      this.$refs.countDown.start();
    },
    // 滑块验证关闭
    onSliderClose(data) {
      this.sliderVisable = false;
      if (data?.isExpired) {
        setTimeout(() => {
          uni.$petro.route({
            type: 'back',
            delta: 1, // 默认是1，表示返回上一页，可不传delta
          });
        }, 500);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-verification {
  background: #ffffff;
  padding: 0 0 36rpx;
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;
  position: fixed;
}

.header {
  width: 100%;
  height: 480rpx;
  background: linear-gradient(53deg, rgba(121, 68, 255, 0.06) 0%, rgba(58, 145, 255, 0.15) 40%, rgba(41, 81, 154, 0.48) 100%);
  display: flex;
  align-items: flex-start;
  padding: 260rpx 32rpx 0;
  box-sizing: border-box;

  &-logo {
    width: 96rpx;
    height: 96rpx;
    margin-right: 32rpx;
  }

  &-value {
    flex: 1;
  }

  &-title {
    // flex: 1;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 44rpx;
    color: #333333;
    line-height: 62rpx;
  }

  &-subTitle {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #999999;
    line-height: 32rpx;
    margin-top: 4rpx;
  }
}

.title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #000000;
  line-height: 42rpx;
  margin-top: 24rpx;
  text-align: center;
}

.subTitle {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #999999;
  line-height: 32rpx;
  margin-top: 8rpx;
  text-align: center;
}

::v-deep .u-code-input__item {
  width: 96rpx !important;
  height: 96rpx !important;
  background: #f5f6f7 !important;
  border: none !important;
  border-radius: 16rpx !important;

  & > text {
    font-size: 60rpx !important;
    color: #333 !important;
  }
}

.code {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: center;
  margin: 40rpx 0 24rpx;
  padding: 0 32rpx;

  .box {
    width: 96rpx;
    height: 96rpx;
    background: #f5f6f7;
    border-radius: 16rpx;
    box-sizing: border-box;
    margin-right: 32rpx;
    line-height: 96rpx;
    font-size: 60rpx;
    color: #333;
    text-align: center;

    &-last {
      margin: 0;
    }

    .line {
      font-size: 56rpx;
      opacity: 0;
      animation-name: donghua;
      animation-timing-function: linear;
      animation-iteration-count: infinite;
      animation-duration: 0.5s;
      animation-direction: alternate;
    }
    @keyframes donghua {
      0% {
        opacity: 1;
      }
      100% {
        opacity: 0;
      }
    }
  }

  .input {
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    opacity: 0;
    padding: 0;
  }
}

.time {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 28rpx;
  display: flex;
  margin-left: 136rpx;

  &-tips {
    color: #fa1919;
    margin-left: 20rpx;
  }

  &-countdown {
    display: flex;

    ::v-deep .u-count-down__text {
      font-size: 24rpx;
      color: #999999;
      line-height: 28rpx;
    }
  }
}

button {
  height: 98rpx;
  background: #fa1919;
  border: none;
  border-radius: 49rpx;
  text-align: center;
  line-height: 98rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  margin: 48rpx 32rpx 64rpx;
  letter-spacing: 18rpx;
  text-indent: 18rpx;

  &:active {
    opacity: 0.8;
  }
}
</style>
