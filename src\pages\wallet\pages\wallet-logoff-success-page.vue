<template>
  <div class="page-container">
    <u-navbar :title="'注销结果'" :autoBack="true" :placeholder="true" :bgColor="'transparent'"></u-navbar>
    <view class="content">
      <view class="placeholder"></view>
      <view class="schematic-icon">
        <image :src="type[state].icon" mode="scaleToFill" />
      </view>
      <view class="success-tip"> {{ type[state].title }}</view>
      <view class="state-tip"> {{ type[state].content }}</view>
      <scroll-view class="scroll" scroll-y>
        <div class="reason" v-for="item in msg" :key="item">
          <div class="license-plate">{{ item.carLicence }}</div>
          <div class="reason-text">{{ item.openResult }}</div>
        </div>
      </scroll-view>
    </view>

    <view class="btn">
      <button hover-class="hover-class" @click="onLogout">确定</button>
    </view>
  </div>
</template>

<script>
export default {
  name: 'contacts-success-page',
  data() {
    return {
      msg: [],
      type: {
        success: {
          title: '注销成功',
          content: '单位卡注销成功，该业务已关闭，如需要开展其他业务，为了你的账户安全，你需要重新登录完成校验',
          icon: '../../../static/success.png',
        },
        fail: {
          title: '注销失败',
          content: '单位卡注销失败，您可重新注销或咨询客服处理。',
          icon: '../../../static/opening-error.png',
        },
      },
      state: 'success',
    };
  },
  onLoad(options) {
    this.msg = options.data ? JSON.parse(options.data).msg : [];
    if (!this.msg || this.msg.length === 0) {
      this.state = 'success';
      setTimeout(() => {
        uni.$petro.logout(true);
      }, 1000);
    } else {
      this.state = 'fail';
    }
  },
  mounted() { },
  methods: {
    onLogout() {
      uni.$petro.logout(true);
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;

  .content {
    display: flex;
    flex-direction: column;
    flex: 1;
    overflow: hidden;
    padding-bottom: 40rpx;
  }

  .scroll {
    flex: 1;

    .reason {
      width: 610rpx;
      height: 159rpx;
      background: #f4f5f6;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 32rpx;
      margin: 0 auto 24rpx;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;

      .license-plate {
        font-weight: 500;
        font-size: 32rpx;
        color: #333333;
        line-height: 38rpx;
        font-style: normal;
        text-transform: none;
      }

      .reason-text {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 400;
        font-size: 24rpx;
        color: #666666;
        line-height: 28rpx;
        font-style: normal;
        text-transform: none;
      }
    }
  }

  .placeholder {
    width: 100%;
    height: 24rpx;
    background: #f0f1f5;
  }

  .schematic-icon {
    width: 96rpx;
    height: 96rpx;
    border-radius: 0;
    margin: 148rpx auto 0;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .success-tip {
    width: 100%;
    margin: 16rpx auto 0;
    font-size: 32rpx;
    font-weight: 500;
    color: #333333;
    line-height: 38rpx;
    text-align: center;
    font-style: normal;
    text-transform: none;
  }

  .state-tip {
    width: 600rpx;
    height: 80rpx;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
    font-style: normal;
    text-transform: none;
    margin: 32rpx auto 78rpx;
    text-align: center;
  }

  .btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 50rpx;
    width: 100%;
    background: #fff;
    padding-bottom: env(safe-area-inset-bottom);

    button {
      width: 642rpx;
      height: 92rpx;
      background: #ffffff;
      border-radius: 200rpx 200rpx 200rpx 200rpx;
      border: 2rpx solid #eeeeee;
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 38rpx;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .hover-class {
      background: #e7e7e7;
    }
  }
}
</style>
