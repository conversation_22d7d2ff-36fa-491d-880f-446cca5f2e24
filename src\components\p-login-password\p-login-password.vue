<template>
  <div class="p-login-password">
    <view class="header">
      <image class="header-logo" src="@/static/icon-logo.png"></image>
      <!-- <view class="header-title">欢迎来到中油智行</view> -->
      <view class="header-value">
        <view class="header-title">欢迎来到中油智行</view>
        <view class="header-subTitle">智行在手&nbsp;便捷无忧</view>
      </view>
    </view>

    <view class="container">
      <input placeholder-class="input-placeholder" v-model="mobile" type="number" placeholder="请输入手机号" />
      <view class="password" v-if="loginVisable">
        <input placeholder-class="input-placeholder" v-model="password" :password="!passwordVisable" placeholder="请输入密码" />
        <img
          class="password-icon"
          :src="require(`@/static/icon-${!passwordVisable ? 'eyeClosed.png' : 'eye.png'}`)"
          @click="passwordVisable = !passwordVisable"
        />
      </view>
      <view class="agree" @click="isAgree = !isAgree">
        <image class="agree-checkbox" :src="`/static/icon-${isAgree ? 'agree' : 'disagree'}.png`"></image>
        <view class="agree-content">
          我已阅读并同意
          <view @click.stop="onViewAgreement(2)">《用户服务协议》</view>
          和
          <view @click.stop="onViewAgreement(1)">《隐私政策》</view>
        </view>
      </view>
      <view v-if="loginVisable">
        <button class="btn-login" @click="onLogin()">登录</button>
        <view class="forget">
          <view @click="onForgetPassword()">忘记密码？</view>
        </view>
      </view>
      <button v-else @click="onGetCode()">获取验证码</button>
      <!-- TODO 1.0.1版本不做 -->
      <!-- <view class="methods" @click="onLoginClick()">本机号码一键登录</view>
      <view class="methods" @click="onLoginCode()">短信验证码登录</view> -->
    </view>

    <view class="footer">
      <view class="footer-value" @click="onChangeView()">{{ !loginVisable ? '登录' : '注册' }}</view>
      <view class="footer-line"></view>
      <view class="footer-value" @click="onMakePhone()">帮助</view>
    </view>
  </div>
</template>

<script>
import { encrypt } from '@/utils';

export default {
  props: {
    locationInfo: {
      type: Object,
      default() {
        return {};
      },
    },
  },
  data() {
    return {
      mobile: '', // 账号
      password: '', // 账号密码
      passwordVisable: false, // 是否显示密码
      isAgree: false, // 是否同意协议
      loginVisable: true, // 登录还是注册
      messageType: '3', // 1-用户注册 2-忘记密码 3-登录 4-换设备验证
      location: {}, // 定位信息
    };
  },
  watch: {
    locationInfo(newVal, oldVal) {
      console.log('locationInfo', newVal, oldVal);
      this.location = newVal;
    },
  },
  methods: {
    // 一键登录
    onLoginClick() {
      uni.$petro.route({ url: '/pages/login-click/login-click', type: 'redirectTo' });
    },
    // 短信验证码登录
    onLoginCode() {
      uni.$petro.route({ url: '/pages/login-code/login-code', type: 'redirectTo' });
    },
    // 查看协议
    async onViewAgreement(value) {
      try {
        const res = await uni.$petro.http('user.agreement.h5', {
          agrmtType: value,
          regionCode: '510100',
        });
        console.log('res', res);
        if (!res?.data?.fileUrl) return uni.showToast({ content: '查看失败' });

        const res1 = await uni.$petro.downloadOpenFile(res.data.fileUrl);
        console.log('downloadOpenFile', res1);
      } catch (e) {}
    },
    // 帮助 -- 跳转客服电话956100
    onMakePhone() {
      uni.makePhoneCall({
        phoneNumber: '956100',
      });
    },
    // 切换登录还是注册
    onChangeView() {
      this.loginVisable = !this.loginVisable;
      this.messageType = this.loginVisable ? '3' : '1';
    },
    // 数据校验
    verification() {
      if (!uni.$u.test.mobile(this.mobile)) {
        uni.showToast({ content: '请输入正确的手机号' });
        return false;
      }

      if (this.loginVisable && !this.password) {
        uni.showToast({ content: '请输入登录密码' });
        return false;
      }

      if (!this.isAgree) {
        uni.showToast({ content: '请同意协议' });
        return false;
      }

      return true;
    },
    // 登录 -- 换设备登录 v420
    async onLogin() {
      if (!this.verification()) return;

      try {
        /**
         * loginType 1—使用登录临时码及短信登录(app) 3—手机号+密码登录(app) 4—短信+换设备短信临时码(app) 5—换设备人脸临时码(app)
         * mobile 手机
         * tempCode 临时码 若loginType值为1、4、5，该字段必填。
         * messageCode 短信码 若loginType值为1、4，该字段必填。
         * password 登录密码。若loginType值为3，该字段必填。
         * loginLocation 登录地区
         * loginGps 登录gps坐标
         */
        const res = await uni.$petro.http(
          'user.login.h5',
          {
            loginType: '3',
            mobile: this.mobile,
            password: `ENC(${encrypt(this.password)})`,
            loginLocation: this.location?.address?.province + this.location?.address?.city,
            loginGps: this.location?.longitude ? `${this.location?.longitude},${this.location?.latitude}` : '',
          },
          { riskField: true },
        );
        console.log('res', res);
        if (!res?.success || !res?.data) return uni.showToast({ content: res.message || '登录失败' });
        // 换设备校验状态:0-通过 1-短信验证 2-人脸验证
        if (res.data.authStatus === 0) {
          // 存储用户信息
          await uni.$petro.setTokenInfo(res.data);
        }
        this.$emit('success', { authStatus: res.data.authStatus, mobile: this.mobile, messageType: '4', tempCode: res.data.tempCode });
      } catch (e) {}
    },
    // 登录 - 校验登录密码 v410
    async onCheckPassword() {
      if (!this.verification()) return;

      try {
        /**
         * mobile 手机号
         * password 登录密码
         */
        const res = await uni.$petro.http('user.loginPassword.check.h5', {
          mobile: this.mobile,
          password: `ENC(${encrypt(this.password)})`,
        });
        console.log('res', res);
        if (!res?.success || !res?.data) return uni.showToast({ content: res.message || '登录失败' });

        this.$emit('success', { mobile: this.mobile, messageType: this.messageType, tempCode: res.data.tempCode });
      } catch (e) {}
    },
    // 获取验证码
    onGetCode() {
      if (!this.verification()) return;
      this.$emit('success', { mobile: this.mobile, messageType: this.messageType });
    },
    // 忘记密码
    onForgetPassword() {
      this.messageType = '2';
      this.loginVisable = false;
    },
  },
};
</script>

<style scoped lang="scss">
.p-login-password {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  padding: 0 0 36rpx;
  box-sizing: border-box;
  position: relative;

  .header {
    width: 100%;
    height: 480rpx;
    background: linear-gradient(53deg, rgba(121, 68, 255, 0.06) 0%, rgba(58, 145, 255, 0.15) 40%, rgba(41, 81, 154, 0.48) 100%);
    display: flex;
    align-items: flex-start;
    padding: 260rpx 32rpx 0;
    box-sizing: border-box;

    &-logo {
      width: 96rpx;
      height: 96rpx;
      margin-right: 32rpx;
    }

    &-value {
      flex: 1;
    }

    &-title {
      // flex: 1;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 44rpx;
      color: #333333;
      line-height: 62rpx;
    }

    &-subTitle {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #999999;
      line-height: 32rpx;
      margin-top: 4rpx;
    }
  }

  .container {
    padding: 32rpx;
  }

  input {
    width: 100%;
    height: 96rpx;
    padding: 0 32rpx;
    box-sizing: border-box;
    background: #f5f6f7;
    border-radius: 16rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 40rpx;
    color: #333333;
    line-height: 96rpx;
  }

  ::v-deep .input-placeholder {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 32rpx;
    color: #bfbfbf;
  }

  .password {
    position: relative;
    margin-top: 32rpx;
    height: 96rpx;

    &-icon {
      position: absolute;
      right: 36rpx;
      top: 28rpx;
      width: 40rpx;
      height: 40rpx;
    }
  }

  .agree {
    display: flex;
    text-align: left;
    margin-top: 40rpx;

    &-checkbox {
      width: 36rpx;
      height: 36rpx;
      margin-right: 6rpx;
    }

    &-content {
      flex: 1;
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
      line-height: 36rpx;

      & > view {
        color: #fa1919;
        display: inline-block;
      }
    }
  }

  button {
    height: 98rpx;
    background: #fa1919;
    border: none;
    border-radius: 49rpx;
    text-align: center;
    line-height: 98rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 36rpx;
    color: #ffffff;
    margin: 48rpx 0 64rpx;

    &:active {
      opacity: 0.8;
    }
  }

  .btn-login {
    letter-spacing: 18rpx;
    text-indent: 18rpx;
    margin-bottom: 48rpx;
  }

  .forget {
    display: flex;
    flex-direction: row-reverse;
    font-weight: 400;
    font-size: 28rpx;
    color: #fa1919;
    line-height: 40rpx;
  }

  .methods {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 32rpx;
    margin-bottom: 40rpx;
    text-align: center;
  }

  .footer {
    position: absolute;
    bottom: 96rpx;
    left: 0;
    right: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40rpx;

    &-value {
      font-family: PingFang SC, PingFang SC;
      font-weight: 400;
      font-size: 28rpx;
      color: #333333;
      line-height: 44px;
      margin: 0 48rpx;
    }

    &-line {
      width: 3rpx;
      height: 28rpx;
      background: #ccc;
    }
  }
}

.debug {
  position: fixed;
  top: 15vh;
  left: 0vw;
  z-index: 99999;
  opacity: 0.6;
  color: #ff7575;
}
</style>
