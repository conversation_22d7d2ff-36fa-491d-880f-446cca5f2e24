---
description: 企业小程序前端路由规范
globs: *.vue,*.js,pages.json
alwaysApply: true
---

# 前端路由规范

## 总体描述

本文档规定了企业小程序前端项目的路由配置和使用规范，包括页面配置、路由跳转、页面生命周期和参数传递等内容，确保项目路由管理的一致性和可维护性。

## 应用范围

本文档适用于企业小程序项目的所有前端开发人员，涵盖pages.json配置、页面路由跳转、页面参数传递等路由相关的开发工作。

## 使用要求

开发人员在进行页面配置和路由跳转时必须严格遵循本文档的规范要求，确保路由管理的规范性和一致性。

## 规则1 页面配置规范

### 1.1 pages.json基本结构

**pages.json必须包含以下基本配置**

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom"
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "中油智行",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F8F8F8"
  },
  "tabBar": {
    "color": "#767678",
    "selectedColor": "#FA1919",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png"
      },
      {
        "pagePath": "pages/mine/mine",
        "text": "我的",
        "iconPath": "static/tabbar/mine.png",
        "selectedIconPath": "static/tabbar/mine-active.png"
      }
    ]
  },
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "登录页",
        "path": "pages/login/login"
      }
    ]
  }
}
```

### 1.2 页面路径命名规范

**页面路径必须使用短横线命名法，并放置在独立目录中**

```json
// ✅ 正确示例
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": { "navigationBarTitleText": "首页" }
    },
    {
      "path": "pages/login/login",
      "style": { "navigationBarTitleText": "登录" }
    },
    {
      "path": "pages/user-profile/user-profile",
      "style": { "navigationBarTitleText": "用户资料" }
    },
    {
      "path": "pages/order-detail/order-detail",
      "style": { "navigationBarTitleText": "订单详情" }
    }
  ]
}

// ❌ 错误示例
{
  "pages": [
    {
      "path": "pages/UserProfile",  // 不应使用驼峰命名
      "style": { "navigationBarTitleText": "用户资料" }
    },
    {
      "path": "pages/orderDetail/orderDetail",  // 不应使用驼峰命名
      "style": { "navigationBarTitleText": "订单详情" }
    }
  ]
}
```

### 1.3 页面分包配置

**按业务模块进行分包配置**

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": { "navigationBarTitleText": "首页" }
    },
    {
      "path": "pages/login/login",
      "style": { "navigationBarTitleText": "登录" }
    }
  ],
  "subPackages": [
    {
      "root": "pages/user",
      "pages": [
        {
          "path": "profile/profile",
          "style": { "navigationBarTitleText": "个人资料" }
        },
        {
          "path": "settings/settings",
          "style": { "navigationBarTitleText": "设置" }
        }
      ]
    },
    {
      "root": "pages/order",
      "pages": [
        {
          "path": "list/list",
          "style": { "navigationBarTitleText": "订单列表" }
        },
        {
          "path": "detail/detail",
          "style": { "navigationBarTitleText": "订单详情" }
        }
      ]
    }
  ]
}
```

### 1.4 页面样式配置

**页面样式配置必须包含必要的导航栏设置**

```json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true,
        "backgroundColor": "#F8F8F8",
        "navigationBarBackgroundColor": "#FFFFFF",
        "navigationBarTextStyle": "black",
        "app-plus": {
          "bounce": "none",
          "titleNView": {
            "buttons": [
              {
                "text": "更多",
                "fontSize": "14px",
                "color": "#333333"
              }
            ]
          }
        }
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom",  // 自定义导航栏
        "app-plus": {
          "titleNView": false
        }
      }
    }
  ]
}
```

## 规则2 路由跳转规范

### 2.1 统一使用uni.$petro.route方法

**所有页面跳转必须使用uni.$petro.route方法**

```javascript
// ✅ 正确示例
// 普通跳转
uni.$petro.route({
  url: '/pages/detail/detail',
  type: 'navigateTo',
  params: {
    id: '123',
    from: 'list'
  }
});

// 替换当前页面
uni.$petro.route({
  url: '/pages/result/result',
  type: 'redirectTo',
  params: {
    status: 'success'
  }
});

// 关闭所有页面，打开新页面
uni.$petro.route({
  url: '/pages/index/index',
  type: 'reLaunch'
});

// 返回上一页
uni.$petro.route({
  type: 'navigateBack',
  delta: 1
});

// ❌ 错误示例
// 不要直接使用uni原生方法
uni.navigateTo({
  url: '/pages/detail/detail?id=123'
});

// 不要使用字符串拼接参数
uni.navigateTo({
  url: '/pages/detail/detail?id=' + id + '&from=' + from
});
```

### 2.2 路由类型定义

**路由跳转类型必须符合以下定义**

| 类型 | 说明 | 场景 |
| ---- | ---- | ---- |
| navigateTo | 保留当前页面，跳转到新页面 | 需要返回的场景，如列表到详情 |
| redirectTo | 关闭当前页面，跳转到新页面 | 不需要返回的场景，如登录成功后跳转 |
| reLaunch | 关闭所有页面，打开新页面 | 需要重置导航栈的场景，如切换用户身份 |
| switchTab | 跳转到tabBar页面 | 切换底部导航栏页面 |
| navigateBack | 返回上一页或多级页面 | 返回场景，如取消操作、完成操作 |

```javascript
// 示例：根据场景选择合适的跳转类型
// 场景1：从列表进入详情页，需要返回
uni.$petro.route({
  url: '/pages/detail/detail',
  type: 'navigateTo',
  params: { id: '123' }
});

// 场景2：登录成功后跳转到首页，不需要返回登录页
uni.$petro.route({
  url: '/pages/index/index',
  type: 'redirectTo'
});

// 场景3：切换用户身份，需要重置所有页面
uni.$petro.route({
  url: '/pages/index/index',
  type: 'reLaunch'
});

// 场景4：切换到底部导航栏的"我的"页面
uni.$petro.route({
  url: '/pages/mine/mine',
  type: 'switchTab'
});

// 场景5：操作完成后返回上一页
uni.$petro.route({
  type: 'navigateBack',
  delta: 1
});
```

### 2.3 路由拦截与权限控制

**实现路由拦截和权限控制**

```javascript
// 在main.js中实现路由拦截
import Vue from 'vue';
import store from './services/store';

// 路由拦截器
const whiteList = ['/pages/login/login', '/pages/register/register'];

// 页面跳转前拦截
uni.$petro.routeInterceptor = async (options) => {
  // 获取token
  const token = await uni.$petro.getTokenInfo();
  
  // 判断是否需要登录
  const needLogin = !whiteList.includes(options.url) && !token;
  
  if (needLogin) {
    // 需要登录，跳转到登录页
    return {
      url: '/pages/login/login',
      type: 'redirectTo',
      params: {
        redirect: encodeURIComponent(JSON.stringify({
          url: options.url,
          params: options.params
        }))
      }
    };
  }
  
  // 判断是否有权限访问
  if (options.url.startsWith('/pages/admin/') && !store.getters['roles/isAdmin']) {
    // 无权限访问，跳转到无权限页面
    return {
      url: '/pages/no-permission/no-permission',
      type: 'redirectTo'
    };
  }
  
  // 正常跳转
  return options;
};

// 使用示例
async function goToAdminPage() {
  // 会自动经过路由拦截器处理
  uni.$petro.route({
    url: '/pages/admin/dashboard',
    type: 'navigateTo'
  });
}
```

## 规则3 页面生命周期规范

### 3.1 页面生命周期使用规范

**页面必须正确使用UniApp页面生命周期钩子**

```javascript
export default {
  data() {
    return {
      pageData: null,
      loading: false
    };
  },
  
  /**
   * 页面加载时触发，只触发一次
   * 用于接收页面参数和初始化数据
   */
  onLoad(options) {
    console.log('页面加载: onLoad');
    // 接收页面参数
    this.id = options.id;
    // 初始化数据
    this.initData();
  },
  
  /**
   * 页面显示时触发，每次页面显示都会触发
   * 用于刷新页面数据或更新状态
   */
  onShow() {
    console.log('页面显示: onShow');
    // 刷新数据
    this.refreshData();
  },
  
  /**
   * 页面初次渲染完成时触发，只触发一次
   * 用于执行需要DOM准备好后的操作
   */
  onReady() {
    console.log('页面就绪: onReady');
    // DOM操作
    this.initUI();
  },
  
  /**
   * 页面隐藏时触发
   * 用于暂停或停止一些操作
   */
  onHide() {
    console.log('页面隐藏: onHide');
    // 暂停操作
    this.pauseOperations();
  },
  
  /**
   * 页面卸载时触发
   * 用于清理资源和状态
   */
  onUnload() {
    console.log('页面卸载: onUnload');
    // 清理资源
    this.clearResources();
  },
  
  /**
   * 下拉刷新触发
   * 用于刷新页面数据
   */
  onPullDownRefresh() {
    console.log('下拉刷新: onPullDownRefresh');
    // 刷新数据
    this.refreshData().finally(() => {
      // 停止下拉刷新
      uni.stopPullDownRefresh();
    });
  },
  
  /**
   * 上拉触底触发
   * 用于加载更多数据
   */
  onReachBottom() {
    console.log('上拉触底: onReachBottom');
    // 加载更多
    if (!this.loading && this.hasMore) {
      this.loadMore();
    }
  },
  
  /**
   * 用户点击分享按钮触发
   * 用于自定义分享内容
   */
  onShareAppMessage() {
    return {
      title: '分享标题',
      path: '/pages/index/index',
      imageUrl: '/static/share.png'
    };
  },
  
  methods: {
    /**
     * 初始化数据
     */
    async initData() {
      try {
        this.loading = true;
        // 获取数据
        await this.fetchData();
      } catch (error) {
        console.error('初始化数据失败:', error);
        uni.showToast({
          title: '加载失败，请重试',
          icon: 'none'
        });
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      // 实现刷新逻辑
    },
    
    /**
     * 加载更多数据
     */
    async loadMore() {
      // 实现加载更多逻辑
    },
    
    /**
     * 初始化UI
     */
    initUI() {
      // 实现UI初始化逻辑
    },
    
    /**
     * 暂停操作
     */
    pauseOperations() {
      // 实现暂停逻辑
    },
    
    /**
     * 清理资源
     */
    clearResources() {
      // 实现清理逻辑
    }
  }
};
```

### 3.2 页面返回拦截

**实现页面返回拦截处理**

```javascript
export default {
  data() {
    return {
      formData: {},
      hasChanged: false
    };
  },
  
  onLoad() {
    // 监听表单变化
    this.originalFormData = JSON.stringify(this.formData);
  },
  
  // 监听返回按钮事件
  onBackPress(options) {
    // 判断表单是否有修改
    const currentFormData = JSON.stringify(this.formData);
    this.hasChanged = currentFormData !== this.originalFormData;
    
    if (this.hasChanged) {
      // 显示确认对话框
      uni.showModal({
        title: '提示',
        content: '表单已修改，确定要放弃修改吗？',
        success: (res) => {
          if (res.confirm) {
            // 用户确认放弃修改，返回上一页
            uni.navigateBack();
          }
        }
      });
      
      // 返回true阻止默认返回行为
      return true;
    }
    
    // 返回false不阻止默认返回行为
    return false;
  },
  
  methods: {
    // 保存表单
    saveForm() {
      // 保存逻辑
      this.hasChanged = false;
      uni.navigateBack();
    }
  }
};
```

## 规则4 参数传递规范

### 4.1 页面间参数传递

**使用uni.$petro.route的params参数进行传递**

```javascript
// ✅ 正确示例
// 传递参数
uni.$petro.route({
  url: '/pages/detail/detail',
  type: 'navigateTo',
  params: {
    id: '123',
    title: '商品详情',
    data: {
      price: 99.9,
      stock: 100
    }
  }
});

// 接收参数
export default {
  data() {
    return {
      id: '',
      title: '',
      price: 0,
      stock: 0
    };
  },
  
  onLoad(options) {
    // 接收基本参数
    this.id = options.id;
    this.title = options.title;
    
    // 接收复杂参数
    if (options.data) {
      const data = JSON.parse(options.data);
      this.price = data.price;
      this.stock = data.stock;
    }
  }
};

// ❌ 错误示例
// 不要使用字符串拼接参数
uni.navigateTo({
  url: '/pages/detail/detail?id=' + id + '&title=' + encodeURIComponent(title)
});
```

### 4.2 页面返回参数传递

**使用事件总线或页面栈传递返回参数**

```javascript
// 方法1：使用事件总线传递返回参数
// 在main.js中创建事件总线
Vue.prototype.$eventBus = new Vue();

// 在表单页面提交后发送事件
methods: {
  submitForm() {
    // 提交表单
    const result = {
      success: true,
      data: this.formData
    };
    
    // 发送事件
    this.$eventBus.$emit('formSubmitted', result);
    
    // 返回上一页
    uni.$petro.route({
      type: 'navigateBack'
    });
  }
}

// 在列表页面监听事件
onShow() {
  // 监听表单提交事件
  this.$eventBus.$on('formSubmitted', this.handleFormSubmitted);
},

onUnload() {
  // 移除事件监听
  this.$eventBus.$off('formSubmitted', this.handleFormSubmitted);
},

methods: {
  handleFormSubmitted(result) {
    if (result.success) {
      // 处理成功结果
      this.refreshList();
    }
  }
}

// 方法2：使用页面栈传递返回参数
// 在表单页面提交后设置上一页的数据
methods: {
  submitForm() {
    // 提交表单
    const result = {
      success: true,
      data: this.formData
    };
    
    // 获取页面栈
    const pages = getCurrentPages();
    // 获取上一页
    const prevPage = pages[pages.length - 2];
    
    // 设置上一页的数据
    if (prevPage) {
      prevPage.$vm.formResult = result;
    }
    
    // 返回上一页
    uni.$petro.route({
      type: 'navigateBack'
    });
  }
}

// 在列表页面处理返回数据
data() {
  return {
    formResult: null
  };
},

onShow() {
  // 检查是否有返回数据
  if (this.formResult) {
    // 处理返回数据
    if (this.formResult.success) {
      this.refreshList();
    }
    // 清空返回数据
    this.formResult = null;
  }
}
```

### 4.3 URL参数编码处理

**处理特殊字符和中文参数**

```javascript
// 传递包含特殊字符的参数
uni.$petro.route({
  url: '/pages/detail/detail',
  type: 'navigateTo',
  params: {
    title: '特殊字符&中文参数',
    keywords: '手机,电脑,平板',
    filter: JSON.stringify({
      price: [100, 1000],
      brands: ['Apple', 'Samsung']
    })
  }
});

// 接收并解码参数
export default {
  data() {
    return {
      title: '',
      keywords: [],
      filter: {}
    };
  },
  
  onLoad(options) {
    // 接收基本参数
    this.title = options.title;
    
    // 处理逗号分隔的参数
    if (options.keywords) {
      this.keywords = options.keywords.split(',');
    }
    
    // 解析JSON参数
    if (options.filter) {
      try {
        this.filter = JSON.parse(options.filter);
      } catch (error) {
        console.error('解析filter参数失败:', error);
      }
    }
  }
};
```

## 规则5 路由优化与最佳实践

### 5.1 预加载页面

**使用预加载提高页面跳转体验**

```javascript
// 在列表页面预加载详情页
export default {
  data() {
    return {
      list: []
    };
  },
  
  onReady() {
    // 预加载详情页
    this.preloadDetailPage();
  },
  
  methods: {
    /**
     * 预加载详情页
     */
    preloadDetailPage() {
      // 使用uni.preloadPage预加载页面
      uni.preloadPage({
        url: '/pages/detail/detail'
      });
    },
    
    /**
     * 跳转到详情页
     */
    goToDetail(item) {
      uni.$petro.route({
        url: '/pages/detail/detail',
        type: 'navigateTo',
        params: {
          id: item.id
        }
      });
    }
  }
};
```

### 5.2 页面栈管理

**合理管理页面栈，避免页面栈溢出**

```javascript
// 检查页面栈深度并处理
function checkPageStackAndNavigate(options) {
  // 获取当前页面栈
  const pages = getCurrentPages();
  
  // 检查页面栈深度（微信小程序最多10层）
  if (pages.length >= 9) {
    // 页面栈即将溢出，使用redirectTo代替navigateTo
    return uni.$petro.route({
      url: options.url,
      type: 'redirectTo',
      params: options.params
    });
  }
  
  // 正常跳转
  return uni.$petro.route(options);
}

// 使用示例
function goToDetailPage(id) {
  checkPageStackAndNavigate({
    url: '/pages/detail/detail',
    type: 'navigateTo',
    params: { id }
  });
}
```

### 5.3 路由动画配置

**配置页面切换动画提升用户体验**

```javascript
// 在pages.json中配置页面切换动画
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "app-plus": {
          "animationType": "slide-in-right",
          "animationDuration": 300
        }
      }
    }
  ],
  "globalStyle": {
    "app-plus": {
      "animationType": "slide-in-right",
      "animationDuration": 300
    }
  }
}

// 在路由跳转时指定动画
uni.$petro.route({
  url: '/pages/detail/detail',
  type: 'navigateTo',
  params: { id: '123' },
  animationType: 'slide-in-bottom',
  animationDuration: 300
});
```

## 总结

本规范文档涵盖了企业小程序前端项目的路由配置和使用规范，包括页面配置、路由跳转、页面生命周期和参数传递等内容。开发人员必须严格遵循这些规范，确保路由管理的一致性和可维护性。

**重要提醒：**
1. 所有页面跳转必须使用uni.$petro.route方法
2. 页面路径必须使用短横线命名法，并放置在独立目录中
3. 页面必须正确使用UniApp页面生命周期钩子
4. 参数传递必须使用uni.$petro.route的params参数
5. 合理管理页面栈，避免页面栈溢出
6. 实现路由拦截和权限控制，确保页面访问安全