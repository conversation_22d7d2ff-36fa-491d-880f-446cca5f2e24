<template>
  <div class="page-container">
    <u-navbar :title="'订单详情'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <view class="page-order-detail">
          <div class="content-view">
            <div class="order-item">
              <div class="detail-top">
                <div class="name">
                  <div class="title text-overflow">{{ orderInfo.transactionPlaceName }}</div>
                  <u-icon name="arrow-right" color="#999999" size="14"></u-icon>
                </div>
                <div class="status">{{ GIFT_CARD_ORDER_STATUS[orderInfo.orderStatus] }}</div>
              </div>
              <div class="detail-content" v-for="(goodItem, idx) in orderInfo.giftCardItems" :key="idx">
                <div class="detail-left">
                  <image class="detail-left-img" src="/static/images/gift-card-order-cover.png" />
                  <div class="order-name">
                    <div class="name">{{ goodItem.cardStyleName }}</div>
                      <!-- 仅最后一个 goodItem 显示 type-lable -->
                        <div class="type-lable" v-if="idx === orderInfo.giftCardItems.length - 1">
                          <template v-if="orderInfo.cardTypeNo === 4">
                              <span class="color-block" style="background-color: #6BBE92; color: #FFFFFF;">昆仑E享卡</span> <!-- 柔和绿色 -->
                          </template>
                          <template v-else-if="orderInfo.cardTypeNo === 5">
                              <span class="color-block" style="background-color: #A3C9F1; color: #FFFFFF;">礼品券</span> <!-- 柔和蓝色 -->
                          </template>
                          <template v-else-if="orderInfo.cardTypeNo === 13">
                              <span class="color-block" style="background-color: #F7A899; color: #FFFFFF;">提货券</span> <!-- 柔和橙红色 -->
                          </template>
                          <template v-else-if="orderInfo.cardTypeNo === 12">
                              <span class="color-block" style="background-color: #F9D278; color: #FFFFFF;">福利卡</span> <!-- 柔和黄色 -->
                          </template>
                          <template v-else>
                              <span class="color-block" style="background-color: #C4C4C4; color: #FFFFFF;">{{ orderInfo.cardTypeNo }}</span> <!-- 柔和灰色 -->
                           </template>
                        </div>
                  </div>
                </div>
                <div class="detail-price">
                  <div class="unitPrice">&yen;{{ goodItem.faceValue }}</div>
                  <div class="litre">{{ 'x ' + goodItem.cardNum + '张' }}</div>
                </div>
              </div>
              <div class="payment-row">
                <div></div>
                <div class="amount">
                  <div>实付总额：</div>
                  <div>&yen;</div>
                  <div>{{ orderInfo.realAmount || 0 }}</div>
                </div>
              </div>
              <div class="time-row">
                <div class="car-num">创建时间</div>
                <div class="time">{{ orderInfo.createTime }}</div>
              </div>
            </div>

            <div class="details-price">
              <div class="details-price-item">
                <div class="item-left">完成时间</div>
                <div class="item-right">{{ orderInfo.orderTime || '' }}</div>
              </div>
              <div class="details-price-item">
                <div class="item-left">订单编号</div>
                <div class="item-right">
                  <div class="item-right-text">{{ orderInfo.orderNo || '' }}</div>
                  <div class="copy" @click="copyProductNo(orderInfo.orderNo)">复制</div>
                </div>
              </div>
              <div class="details-price-item">
                <div class="item-left">支付渠道</div>
                <div class="item-right">{{ GIFT_CARD_ORDER_CHANNEL[orderInfo.sourceChannelNo] || '' }}</div>
              </div>
            </div>
          </div>
        </view>
      </petro-layout>
    </div>
  </div>
</template>

<script>
import { GIFT_CARD_ORDER_CHANNEL } from '@/services/enum';
import { getGiftCardOrderDetailApi } from '@/services/http';

export default {
  name: 'order-detail',
  computed: {},
  components: {},
  data() {
    return {
      GIFT_CARD_ORDER_CHANNEL: GIFT_CARD_ORDER_CHANNEL,
      query: {},
      // 订单详情
      orderInfo: null,
    };
  },
  onLoad(query) {
    this.query = query;
    try {
      if (this.query?.data) this.orderInfo = JSON.parse(this.query?.data);
    } catch (err) {
      console.error(err);
    }
    // this.getData();
  },
  methods: {
    // 获取订单详情
    async getData() {
      try {
        const params = {
          stationCode: this.query?.stationCode,
          orderNo: this.query?.orderNo,
        };

        const { success, data } = await getGiftCardOrderDetailApi(params);

        if (!success) return;

        this.orderInfo = data;
      } catch (error) {
        console.log(error);
      }
    },
    // 复制
    copyProductNo(value) {
      uni.setClipboardData({
        data: value,
        success: () => {
          uni.showToast({
            title: '复制成功',
          });
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  height: 100vh;
  background: #f7f7fb;
  display: flex;
  flex-direction: column;

  .page-content {
    flex: 1;
    overflow: hidden;

    .page-order-detail {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;

      .content-view {
        width: 100%;
        flex: 1;
        padding: 32rpx;
        overflow-y: auto;
        box-sizing: border-box;

        .order-item {
          padding: 20rpx 30rpx;
          border-radius: 16rpx;
          background: #ffffff;

          &:not(:first-child) {
            margin-top: 24rpx;
          }

          .detail-top {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .name {
              display: flex;
              align-items: center;
              overflow: hidden;

              image {
                width: 33rpx;
                height: 32rpx;
                flex-shrink: 0;
              }

              .title {
                margin-left: 8rpx;
                margin-right: 10rpx;
                font-size: 28rpx;
                font-weight: bold;
                color: #333333;
                line-height: 40rpx;
              }
            }

            .status {
              margin-left: 40rpx;
              flex-shrink: 0;
              font-size: 24rpx;
              color: #fa1919;
            }
          }

          .detail-content {
            margin-top: 16rpx;

            display: flex;
            justify-content: space-between;

            .detail-left {
              margin-right: 20rpx;
              flex: 1;
              display: flex;

              .detail-left-text {
                width: 78rpx;
                height: 78rpx;
                background: #f0f0f0;
                border-radius: 16rpx;
                display: flex;
                align-items: center;
                justify-content: center;

                span:nth-child(1) {
                  color: #333333;
                  font-size: 32rpx;
                  font-weight: bold;
                }

                span:nth-child(2) {
                  color: #333333;
                  font-size: 20rpx;
                  line-height: 20rpx;
                  margin-top: -20rpx;
                }
              }

              .detail-left-img {
                width: 78rpx;
                height: 78rpx;
                border-radius: 16rpx;
              }

              .order-name {
                flex: 1;
                padding-top: 4rpx;
                margin-left: 20rpx;
                display: flex;
                //flex-direction: row;
                justify-content: space-between;

                .name {
                  font-size: 28rpx;
                  font-weight: 400;
                  color: #333333;
                }
                .type-lable {
                  width: 80px;
                  height: 30px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 14px;
                  font-weight: bold;
                  color: #FFFFFF; /* 字体颜色白色 */
                }
                
                .color-block {
                  width: 100%;
                  height: 100%;
                  text-align: center;
                  line-height: 30px;
                  border-radius: 7px; /* 去掉圆角，锐化角度 */
                }
                .type {
                  margin-top: 6rpx;
                  display: flex;
                  margin-right: 12rpx;

                  span {
                    padding: 0 12rpx;
                    height: 42rpx;
                    line-height: 42rpx;
                    font-size: 22rpx;
                    color: #fa1919;
                    background: #fff3f3;
                    border-radius: 8rpx;
                  }
                }
              }
            }

            .detail-price {
              text-align: right;

              .unitPrice {
                margin-top: 4rpx;
                font-size: 28rpx;
                font-weight: 400;
                color: #333333;
                line-height: 40rpx;
              }

              .litre {
                font-size: 20rpx;
                font-weight: 400;
                color: #666666;
                line-height: 40rpx;
              }
            }
          }

          .time-row {
            height: 48rpx;
            font-weight: 400;
            line-height: 40rpx;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12rpx;

            .car-num {
              font-size: 26rpx;
              color: #666;
              height: 48rpx;
              line-height: 48rpx;
            }

            .time {
              font-size: 26rpx;
              color: #333333;
              height: 48rpx;
              line-height: 48rpx;
            }
          }

          .payment-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 12rpx;

            .amount {
              display: flex;
              flex-direction: row;

              div {
                font-size: 28rpx;
                line-height: 44rpx;
              }

              div:nth-child(1) {
                color: #666666;
              }

              div:nth-child(2) {
                color: #fa1919;
                font-size: 24rpx;
                margin-top: 4rpx;
              }

              div:nth-child(3) {
                color: #fa1919;
                font-weight: bold;
                font-size: 32rpx;
              }
            }
          }

          .relevant-info {
            margin-top: 28rpx;

            .info-item {
              display: flex;
              align-items: center;
              justify-content: space-between;

              div {
                font-size: 26rpx;
                color: #999999;
                line-height: 42rpx;
              }
            }
          }

          .detail-bottom {
            margin-top: 12rpx;

            .payment-status {
              display: flex;
              justify-content: end;

              .time {
                display: flex;
                align-items: center;

                div {
                  font-size: 24rpx;
                }

                div:nth-child(1) {
                  color: #e64f22;
                }

                div:nth-child(2) {
                  margin-left: 12rpx;
                  color: #333;
                }
              }

              .btn {
                padding: 0 12rpx;
                margin-left: 20rpx;
                background: #e64f22;
                color: #ffffff;
                border-color: #e64f22;
                border-radius: 10rpx;
              }
            }
          }
        }

        .details-price {
          margin-top: 24rpx;
          background: #ffffff;
          border-radius: 16rpx;
          padding: 20rpx 28rpx;

          .details-price-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            overflow: hidden;

            .item-left {
              font-size: 28rpx;
              font-weight: 400;
              color: #666666;
              line-height: 67rpx;
              white-space: nowrap;
              margin-right: 40rpx;
            }

            .item-right {
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-size: 28rpx;
              font-weight: 400;
              color: #333333;
              line-height: 67rpx;
              overflow: hidden;

              .item-right-text {
                font-size: 26rpx;
                font-weight: 400;
                color: #333333;
                word-break: break-all;
                line-height: 1.2;
              }

              .copy {
                width: 58rpx;
                height: 32rpx;
                background: #ffffff;
                border-radius: 4rpx;
                border: 1rpx solid #999999;
                text-align: center;
                line-height: 32rpx;
                color: #666;
                font-size: 20rpx;
                margin-left: 10rpx;
                box-sizing: border-box;
                white-space: nowrap;
              }
            }

            .item-right-button {
              padding: 0 20rpx;
              height: 48rpx;
              border-radius: 4rpx;
              border: 1rpx solid #333333;
              line-height: 48rpx;
              font-size: 26rpx;
              font-weight: 400;
              color: #333333;
              text-align: center;
            }

            .color {
              color: #e64f22;
              border: 1rpx solid #e64f22;
            }
          }
        }
      }
    }
  }
}
</style>
