---
description: 企业小程序UI设计规范与组件使用指南
globs: *.vue,*.scss,*.css,static/*
alwaysApply: true
---

# 前端UI设计规范

## 总体描述

本文档规定了企业小程序的UI设计规范，基于UniApp框架和uView UI组件库，包括布局规范、色彩系统、字体规范、组件使用、交互设计等核心内容，确保应用界面的一致性、美观性和用户体验。

## 应用范围

本规范适用于企业小程序项目的所有UI设计和前端开发工作，包括页面布局、组件设计、样式编写、交互效果等视觉和交互相关的开发内容。

## 使用要求

开发人员在进行UI开发时，必须严格遵循本规范的设计原则、组件使用规范和样式编写要求。所有UI组件优先使用uView UI库，自定义样式必须符合设计系统的规范。

## 规则1 色彩规范

### 1.1 主色调系统

**基于中油智行品牌色彩的主色调定义**

```scss
// src/uni.scss - 全局色彩变量
/* 主色调 */
$uni-color-primary: #007aff;        // 主色 - 蓝色
$uni-color-success: #4cd964;        // 成功色 - 绿色
$uni-color-warning: #f0ad4e;        // 警告色 - 橙色
$uni-color-error: #dd524d;          // 错误色 - 红色

/* 中性色 */
$uni-text-color: #333;              // 主文本色
$uni-text-color-inverse: #fff;      // 反色文本
$uni-text-color-grey: #999;         // 辅助文本色
$uni-text-color-placeholder: #808080; // 占位符文本色
$uni-text-color-disable: #c0c0c0;   // 禁用文本色

/* 背景色 */
$uni-bg-color: #fff;                // 主背景色
$uni-bg-color-grey: #f8f8f8;        // 灰色背景
$uni-bg-color-hover: #f1f1f1;       // 悬停背景色
$uni-bg-color-mask: rgba(0, 0, 0, 0.4); // 遮罩背景色

/* 边框色 */
$uni-border-color: #c8c7cc;         // 边框色

/* 项目特定色彩 */
$petro-orange: #FF7033;              // 中油橙色
$petro-red: #FA1919;                // 中油红色
$petro-blue: #007aff;               // 中油蓝色
```

### 1.2 色彩使用规范

**不同场景下的色彩应用规则**

```scss
// ✅ 正确示例 - 规范的色彩使用
.user-card {
  background-color: $uni-bg-color;
  border: 1px solid $uni-border-color;

  &__title {
    color: $uni-text-color;
  }

  &__subtitle {
    color: $uni-text-color-grey;
  }

  &--active {
    border-color: $uni-color-primary;
    background-color: rgba($uni-color-primary, 0.05);
  }

  &--success {
    border-color: $uni-color-success;

    .status-text {
      color: $uni-color-success;
    }
  }

  &--error {
    border-color: $uni-color-error;

    .status-text {
      color: $uni-color-error;
    }
  }
}

// ❌ 错误示例 - 硬编码颜色
.user-card {
  background-color: #ffffff;          // 应使用变量
  border: 1px solid #cccccc;          // 应使用变量
  color: #333333;                     // 应使用变量
}
```

## 规则2 字体规范

### 2.1 字体大小系统

**基于rpx单位的响应式字体系统**

```scss
// 字体大小变量定义
$uni-font-size-sm: 24rpx;           // 小字体 - 辅助信息
$uni-font-size-base: 28rpx;         // 基础字体 - 正文
$uni-font-size-lg: 32rpx;           // 大字体 - 标题

// 扩展字体大小
$font-size-xs: 20rpx;               // 超小字体 - 标签
$font-size-xl: 36rpx;               // 超大字体 - 主标题
$font-size-xxl: 40rpx;              // 特大字体 - 页面标题

// 使用示例
.text-xs { font-size: $font-size-xs; }
.text-sm { font-size: $uni-font-size-sm; }
.text-base { font-size: $uni-font-size-base; }
.text-lg { font-size: $uni-font-size-lg; }
.text-xl { font-size: $font-size-xl; }
.text-xxl { font-size: $font-size-xxl; }
```

### 2.2 字体权重和行高

**统一的字体权重和行高规范**

```scss
// 字体权重
$font-weight-light: 300;            // 细体
$font-weight-normal: 400;           // 常规
$font-weight-medium: 500;           // 中等
$font-weight-bold: 600;             // 粗体

// 行高
$line-height-tight: 1.2;            // 紧凑行高
$line-height-base: 1.5;             // 基础行高
$line-height-loose: 1.8;            // 宽松行高

// 应用示例
.page-title {
  font-size: $font-size-xxl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;
  color: $uni-text-color;
}

.section-title {
  font-size: $font-size-xl;
  font-weight: $font-weight-medium;
  line-height: $line-height-base;
  color: $uni-text-color;
}

.body-text {
  font-size: $uni-font-size-base;
  font-weight: $font-weight-normal;
  line-height: $line-height-base;
  color: $uni-text-color;
}

.caption-text {
  font-size: $uni-font-size-sm;
  font-weight: $font-weight-normal;
  line-height: $line-height-base;
  color: $uni-text-color-grey;
}
```

## 规则3 间距规范

### 3.1 基础间距系统

**基于8rpx基础单位的间距系统**

```scss
// 基础间距单位（8rpx的倍数）
$spacing-xs: 8rpx;                  // 超小间距
$spacing-sm: 16rpx;                 // 小间距
$spacing-base: 24rpx;               // 基础间距
$spacing-lg: 32rpx;                 // 大间距
$spacing-xl: 48rpx;                 // 超大间距
$spacing-xxl: 64rpx;                // 特大间距

// 扩展间距
$spacing-mini: 4rpx;                // 微小间距
$spacing-huge: 96rpx;               // 巨大间距

// 间距工具类
.m-xs { margin: $spacing-xs; }
.m-sm { margin: $spacing-sm; }
.m-base { margin: $spacing-base; }
.m-lg { margin: $spacing-lg; }
.m-xl { margin: $spacing-xl; }

.p-xs { padding: $spacing-xs; }
.p-sm { padding: $spacing-sm; }
.p-base { padding: $spacing-base; }
.p-lg { padding: $spacing-lg; }
.p-xl { padding: $spacing-xl; }

// 方向性间距
.mt-sm { margin-top: $spacing-sm; }
.mb-sm { margin-bottom: $spacing-sm; }
.ml-sm { margin-left: $spacing-sm; }
.mr-sm { margin-right: $spacing-sm; }

.pt-sm { padding-top: $spacing-sm; }
.pb-sm { padding-bottom: $spacing-sm; }
.pl-sm { padding-left: $spacing-sm; }
.pr-sm { padding-right: $spacing-sm; }
```

### 3.2 布局间距规范

**页面和组件布局的间距使用规范**

```scss
// ✅ 正确示例 - 规范的间距使用
.page-container {
  padding: $spacing-lg;              // 页面内边距

  .section {
    margin-bottom: $spacing-xl;      // 区块间距

    &:last-child {
      margin-bottom: 0;
    }
  }

  .card {
    padding: $spacing-base;          // 卡片内边距
    margin-bottom: $spacing-lg;     // 卡片间距

    .card-header {
      margin-bottom: $spacing-sm;   // 标题与内容间距
    }

    .card-content {
      .item {
        margin-bottom: $spacing-sm;  // 列表项间距

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// 表单间距
.form-container {
  .form-item {
    margin-bottom: $spacing-lg;     // 表单项间距

    .label {
      margin-bottom: $spacing-xs;   // 标签与输入框间距
    }
  }

  .form-actions {
    margin-top: $spacing-xl;        // 操作按钮与表单间距

    .button {
      margin-right: $spacing-sm;    // 按钮间距

      &:last-child {
        margin-right: 0;
      }
    }
  }
}
```

## 规则4 uView UI组件使用规范

### 4.1 基础组件使用

**uView UI基础组件的标准使用方式**

```vue
<template>
  <view class="demo-container">
    <!-- 按钮组件 -->
    <u-button
      type="primary"
      size="default"
      :loading="isLoading"
      @click="handleSubmit"
    >
      提交
    </u-button>

    <!-- 输入框组件 -->
    <u-input
      v-model="formData.username"
      placeholder="请输入用户名"
      :border="true"
      :clearable="true"
      @change="handleInputChange"
    />

    <!-- 选择器组件 -->
    <u-picker
      v-model="formData.role"
      :range="roleOptions"
      range-key="label"
      @change="handleRoleChange"
    >
      <u-input
        v-model="roleText"
        placeholder="请选择角色"
        :disabled="true"
        suffix-icon="arrow-down"
      />
    </u-picker>

    <!-- 单元格组件 -->
    <u-cell-group>
      <u-cell
        title="用户名"
        :value="userInfo.name"
        :arrow="true"
        @click="editUserName"
      />
      <u-cell
        title="手机号"
        :value="userInfo.mobile"
        :arrow="true"
        @click="editMobile"
      />
    </u-cell-group>

    <!-- 列表组件 -->
    <u-list>
      <u-list-item
        v-for="item in dataList"
        :key="item.id"
        :title="item.title"
        :note="item.description"
        :thumb="item.avatar"
        :arrow="true"
        @click="handleItemClick(item)"
      />
    </u-list>

    <!-- 加载更多 -->
    <u-loadmore
      :status="loadStatus"
      :load-text="loadText"
      @loadmore="loadMore"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      isLoading: false,
      formData: {
        username: '',
        role: ''
      },
      roleOptions: [
        { label: '管理员', value: 'admin' },
        { label: '普通用户', value: 'user' }
      ],
      roleText: '',
      userInfo: {
        name: '张三',
        mobile: '13800138000'
      },
      dataList: [],
      loadStatus: 'loadmore',
      loadText: {
        loadmore: '点击或上拉加载更多',
        loading: '正在加载...',
        nomore: '没有更多了'
      }
    };
  },

  methods: {
    handleSubmit() {
      this.isLoading = true;
      // 提交逻辑
      setTimeout(() => {
        this.isLoading = false;
      }, 2000);
    },

    handleInputChange(value) {
      console.log('输入值变化:', value);
    },

    handleRoleChange(e) {
      const selectedRole = this.roleOptions[e.detail.value];
      this.formData.role = selectedRole.value;
      this.roleText = selectedRole.label;
    },

    editUserName() {
      // 编辑用户名逻辑
    },

    editMobile() {
      // 编辑手机号逻辑
    },

    handleItemClick(item) {
      console.log('点击列表项:', item);
    },

    loadMore() {
      if (this.loadStatus === 'loadmore') {
        this.loadStatus = 'loading';

        // 模拟加载数据
        setTimeout(() => {
          // 加载完成后更新状态
          this.loadStatus = 'loadmore';
        }, 2000);
      }
    }
  }
};
</script>
```

### 4.2 高级组件使用

**uView UI高级组件的使用规范**

```vue
<template>
  <view class="advanced-demo">
    <!-- 表格组件 -->
    <u-table>
      <u-tr>
        <u-th>姓名</u-th>
        <u-th>年龄</u-th>
        <u-th>操作</u-th>
      </u-tr>
      <u-tr v-for="item in tableData" :key="item.id">
        <u-td>{{ item.name }}</u-td>
        <u-td>{{ item.age }}</u-td>
        <u-td>
          <u-button size="mini" type="primary" @click="editItem(item)">
            编辑
          </u-button>
        </u-td>
      </u-tr>
    </u-table>

    <!-- 弹出层 -->
    <u-popup
      v-model="showPopup"
      mode="center"
      :border-radius="20"
      :closeable="true"
    >
      <view class="popup-content">
        <text class="popup-title">确认删除</text>
        <text class="popup-message">确定要删除这条记录吗？</text>
        <view class="popup-actions">
          <u-button size="default" @click="showPopup = false">
            取消
          </u-button>
          <u-button type="primary" @click="confirmDelete">
            确定
          </u-button>
        </view>
      </view>
    </u-popup>

    <!-- 操作菜单 -->
    <u-action-sheet
      v-model="showActionSheet"
      :list="actionList"
      @click="handleAction"
    />

    <!-- 日期选择器 -->
    <u-datetime-picker
      v-model="showDatePicker"
      mode="date"
      :min-date="minDate"
      :max-date="maxDate"
      @confirm="handleDateConfirm"
      @cancel="showDatePicker = false"
    />

    <!-- 图片上传 -->
    <u-upload
      :file-list="fileList"
      :max-count="9"
      :max-size="5 * 1024 * 1024"
      @afterRead="afterRead"
      @delete="deleteFile"
    />
  </view>
</template>

<script>
export default {
  data() {
    return {
      tableData: [
        { id: 1, name: '张三', age: 25 },
        { id: 2, name: '李四', age: 30 }
      ],
      showPopup: false,
      showActionSheet: false,
      actionList: [
        { text: '编辑', color: '#007aff' },
        { text: '删除', color: '#dd524d' }
      ],
      showDatePicker: false,
      minDate: new Date('2020-01-01').getTime(),
      maxDate: new Date('2030-12-31').getTime(),
      fileList: []
    };
  },

  methods: {
    editItem(item) {
      console.log('编辑项目:', item);
    },

    confirmDelete() {
      // 删除逻辑
      this.showPopup = false;
    },

    handleAction(index) {
      if (index === 0) {
        // 编辑操作
      } else if (index === 1) {
        // 删除操作
        this.showPopup = true;
      }
      this.showActionSheet = false;
    },

    handleDateConfirm(e) {
      console.log('选择的日期:', e);
      this.showDatePicker = false;
    },

    afterRead(event) {
      // 文件上传后处理
      const { file } = event.detail;
      this.fileList.push({
        url: file.url,
        name: file.name
      });
    },

    deleteFile(event) {
      const { index } = event.detail;
      this.fileList.splice(index, 1);
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-content {
  padding: $spacing-xl;
  text-align: center;

  .popup-title {
    font-size: $font-size-xl;
    font-weight: $font-weight-bold;
    color: $uni-text-color;
    margin-bottom: $spacing-sm;
  }

  .popup-message {
    font-size: $uni-font-size-base;
    color: $uni-text-color-grey;
    margin-bottom: $spacing-lg;
  }

  .popup-actions {
    display: flex;
    justify-content: space-around;

    .u-button {
      flex: 1;
      margin: 0 $spacing-xs;
    }
  }
}
</style>
```

## 规则5 自定义组件规范

### 5.1 业务组件设计

**项目特定业务组件的设计规范**

```vue
<!-- p-user-card.vue - 用户卡片组件 -->
<template>
  <view class="p-user-card" :class="cardClass" @click="handleClick">
    <view class="p-user-card__avatar">
      <image
        :src="userInfo.avatar || defaultAvatar"
        mode="aspectFill"
        class="avatar-image"
      />
      <view v-if="showStatus" class="status-dot" :class="statusClass" />
    </view>

    <view class="p-user-card__content">
      <view class="p-user-card__header">
        <text class="name">{{ userInfo.name }}</text>
        <text v-if="userInfo.role" class="role">{{ userInfo.role }}</text>
      </view>

      <view class="p-user-card__info">
        <text class="mobile">{{ formatMobile(userInfo.mobile) }}</text>
        <text v-if="userInfo.department" class="department">
          {{ userInfo.department }}
        </text>
      </view>

      <view v-if="showActions" class="p-user-card__actions">
        <u-button
          size="mini"
          type="primary"
          @click.stop="handleEdit"
        >
          编辑
        </u-button>
        <u-button
          size="mini"
          type="error"
          @click.stop="handleDelete"
        >
          删除
        </u-button>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 用户卡片组件
 * @description 展示用户基本信息的卡片组件
 * @property {Object} userInfo - 用户信息对象
 * @property {Boolean} showStatus - 是否显示状态指示器
 * @property {Boolean} showActions - 是否显示操作按钮
 * @property {String} size - 卡片尺寸 small|default|large
 * @event {Function} click - 点击卡片时触发
 * @event {Function} edit - 点击编辑按钮时触发
 * @event {Function} delete - 点击删除按钮时触发
 */
export default {
  name: 'PUserCard',

  props: {
    userInfo: {
      type: Object,
      required: true,
      default: () => ({})
    },
    showStatus: {
      type: Boolean,
      default: true
    },
    showActions: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'default',
      validator: value => ['small', 'default', 'large'].includes(value)
    }
  },

  computed: {
    cardClass() {
      return {
        [`p-user-card--${this.size}`]: this.size !== 'default',
        'p-user-card--clickable': this.$listeners.click
      };
    },

    statusClass() {
      return {
        'status-dot--online': this.userInfo.status === 'online',
        'status-dot--offline': this.userInfo.status === 'offline',
        'status-dot--busy': this.userInfo.status === 'busy'
      };
    },

    defaultAvatar() {
      return '/static/default-avatar.png';
    }
  },

  methods: {
    formatMobile(mobile) {
      if (!mobile) return '';
      return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    },

    handleClick() {
      this.$emit('click', this.userInfo);
    },

    handleEdit() {
      this.$emit('edit', this.userInfo);
    },

    handleDelete() {
      this.$emit('delete', this.userInfo);
    }
  }
};
</script>

<style lang="scss" scoped>
.p-user-card {
  display: flex;
  align-items: center;
  padding: $spacing-base;
  background: $uni-bg-color;
  border-radius: 16rpx;
  border: 1px solid $uni-border-color;
  margin-bottom: $spacing-sm;
  transition: all 0.3s ease;

  &--clickable {
    &:active {
      transform: scale(0.98);
      opacity: 0.8;
    }
  }

  &--small {
    padding: $spacing-sm;

    .p-user-card__avatar {
      width: 60rpx;
      height: 60rpx;
    }

    .name {
      font-size: $uni-font-size-sm;
    }
  }

  &--large {
    padding: $spacing-lg;

    .p-user-card__avatar {
      width: 120rpx;
      height: 120rpx;
    }

    .name {
      font-size: $font-size-xl;
    }
  }

  &__avatar {
    position: relative;
    width: 80rpx;
    height: 80rpx;
    margin-right: $spacing-base;

    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }

    .status-dot {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 16rpx;
      height: 16rpx;
      border-radius: 50%;
      border: 2rpx solid $uni-bg-color;

      &--online {
        background-color: $uni-color-success;
      }

      &--offline {
        background-color: $uni-text-color-grey;
      }

      &--busy {
        background-color: $uni-color-warning;
      }
    }
  }

  &__content {
    flex: 1;
    min-width: 0;
  }

  &__header {
    display: flex;
    align-items: center;
    margin-bottom: $spacing-xs;

    .name {
      font-size: $uni-font-size-lg;
      font-weight: $font-weight-medium;
      color: $uni-text-color;
      margin-right: $spacing-sm;

      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .role {
      font-size: $uni-font-size-sm;
      color: $uni-color-primary;
      background: rgba($uni-color-primary, 0.1);
      padding: 4rpx 8rpx;
      border-radius: 8rpx;
    }
  }

  &__info {
    margin-bottom: $spacing-sm;

    .mobile,
    .department {
      display: block;
      font-size: $uni-font-size-sm;
      color: $uni-text-color-grey;
      margin-bottom: 4rpx;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  &__actions {
    display: flex;
    gap: $spacing-sm;

    .u-button {
      flex: 1;
    }
  }
}
</style>
```

## 总结

本规范文档详细规定了企业小程序的UI设计规范，包括色彩系统、字体规范、间距规范、uView UI组件使用和自定义组件设计等核心内容。开发人员必须严格遵循这些规范，确保应用界面的一致性、美观性和用户体验。

**重要提醒：**
1. 优先使用uView UI组件库，减少自定义开发
2. 严格遵循色彩和字体规范，保持视觉一致性
3. 使用标准化的间距系统，确保布局规整
4. 自定义组件必须包含完整的文档和示例
5. 所有样式使用SCSS变量，避免硬编码
6. 响应式设计使用rpx单位，适配不同屏幕