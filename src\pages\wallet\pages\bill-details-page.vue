<template>
  <div class="bill-details-page">
    <u-navbar title="账单详情" :autoBack="true" :placeholder="true"></u-navbar>

    <petro-layout ref="layout" :petroKeyboard="true">
      <zyzx-page-bill-details :data="detailsInfo" />
    </petro-layout>
  </div>
</template>

<script>
export default {
  name: 'bill-details-page',
  data() {
    return {
      detailsInfo: {},
    };
  },
  onLoad(options) {
    console.log('🚀 ~ onLoad ~ options:', options);
    // 保存确认下单传递生成的订单信息
    let query = options.data ? JSON.parse(decodeURIComponent(options.data)) : {};
    this.detailsInfo = { ...query.queryInfo };
    console.log('🚀 ~ onLoad ~ this.detailsInfo:', this.detailsInfo);
  },
  onshow() {},
  methods: {},
};
</script>
<style lang="scss" scoped>
.bill-details-page {
  width: 100%;
  height: 100%;
  background: #fff;
}
</style>
