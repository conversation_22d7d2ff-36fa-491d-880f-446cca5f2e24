<script>
export default {
  globalData: {},
  async onLaunch(options) {
    console.log('App Launch');
    // this.hookInit();
    // 其他地方单独重新初始化
    // const app = getApp();
    // app.hookInit();
  },
  onShow() {
    console.log('App Show');
  },
  onHide() {
    console.log('App Hide');
  },
  methods: {
    async hookInit() {
      console.log('hookInit');
      const res = await uni.$petro.getTokenInfo();
      if (!res?.accessToken) return;
      await this.manualRefreshToken();
      console.log('hookInit request');
      await (this.$store || this.$vm?.$store).dispatch('getAccountInfoList', {
        accountType: 1,
        refresh: true,
      });
      await (this.$store || this.$vm?.$store).dispatch('getRoles', true);
      (this.$store || this.$vm?.$store).dispatch('getRolesMap', true);
      await (this.$store || this.$vm?.$store).dispatch('getCompanyList', {
        queryType: 0,
        refresh: true,
      });
      await (this.$store || this.$vm?.$store).dispatch('getUserInfo', true);
      await (this.$store || this.$vm?.$store).dispatch('getWalletInfo', {
        staffRole: 2,
        refresh: true,
      });
    },
    // 自动刷新token
    async manualRefreshToken() {
      try {
        let tokenInfo = await uni.$petro.getTokenInfo();
        let expiresIn = tokenInfo?.expiresIn;
        let currentTime = new Date().getTime();
        if ((expiresIn - currentTime) / (1000 * 24 * 60 * 60) > 1) return;

        const res = await uni.$petro.http('user.refreshTheToken.h5', {
          gsmsToken: this.tokenInfo.gsmsToken,
          token: this.tokenInfo.accessToken,
        });
        if (!res?.success) return;

        await uni.$petro.setTokenInfo({
          expiresIn: res?.data?.expirationTime || tokenInfo.expiresIn,
        });
      } catch (e) {}
    },
  },
};
</script>

<style lang="scss">
@import 'uview-ui/index.scss';

page,
.a-page {
  height: 100%;
  background: #fff;
}

.btn-petro-t {
  font-size: 28rpx;
  height: 88rpx;
  line-height: 88rpx;
  color: #fff;
  border: 2rpx solid #e64f22;
  border-radius: 16rpx;
  background-color: #e64f22 !important;

  &:active {
    color: #fff;
    background-color: #e64f22;
    opacity: 0.8;
  }

  &.disabled {
    opacity: 0.2;
    pointer-events: none;
  }
}

.custom-btn-block {
  border-radius: 8rpx;
  height: 92rpx;
  line-height: 92rpx;
  box-sizing: border-box;
  font-size: 32rpx;
  font-weight: bold;

  &.red {
    color: #ffffff;
    border-color: #fa1919;
    background: #fa1919;
  }

  &.red-plain {
    color: #fa1919;
    border-color: #fa1919;
    background: #ffffff;
  }

  &.orange {
    color: #ffffff;
    border-color: #f79901;
    background: #f79901;
  }

  &.orange-plain {
    color: #f79901;
    border-color: #f79901;
    background: #ffffff;
  }

  &.white {
    color: #333333;
    background: #ffffff;
  }

  &.circle {
    border-radius: 100rpx;
  }

  &.no-border {
    border: none !important;
  }
}

.zyzx-page-roles .u-text__value {
  max-width: 2em;
}
</style>
