<template>
  <div class="account-security-page">
    <u-navbar title="账号与安全" :autoBack="true" :placeholder="true"></u-navbar>

    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-account-security></zyzx-page-account-security>
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import ZyzxPageAccountSecurity from '@/components/zyzx-page-account-security/zyzx-page-account-security';

export default {
  name: 'account-security-page',
  // components: { ZyzxPageAccountSecurity },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.account-security-page {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
