<template>
  <div>
    <u-overlay :show="show">
      <view class="verify-popup">
        <view class="verify-popup-content">
          <div class="slide-verify">
            <!-- 头部 -->
            <div class="slide-header">
              <div class="slide-tips">请完成安全验证</div>
              <img src="@/static/icon-refresh.png" class="refresh-icon" @click="onRefresh" />
              <img src="@/static/icon-slider-close.png" class="refresh-icon" @click="onClose" />
            </div>

            <!-- 滑块主体 -->
            <div class="slide-content">
              <!-- 图片加载遮蔽罩 -->
              <div class="loading" v-if="isLoading">
                <u-loading-icon></u-loading-icon>
              </div>
              <!--验证图片-->
              <img :src="verifyData.backgroundImage" class="verify-image" />
              <!--阻塞块-->
              <img
                :src="verifyData.frontImage"
                class="verify-block"
                :style="{ left: blockLeft + 'px', top: blockTop + 'px' }"
                v-show="!isLoading"
              />
              <!-- 滑动条 -->
              <div class="slider">
                <!-- 滑动按钮 -->
                <div
                  class="slider-button"
                  id="slider-button"
                  :style="{ left: sliderButtonLeft + 'px' }"
                  @touchstart.prevent="touchstart"
                  @touchmove.prevent="touchmove"
                  @touchend.prevent="touchend"
                >
                  <img src="@/static/icon-slider-btn.png" class="slider-button-icon" />
                </div>
                <!--滑动条提示文字-->
                <span class="slider-hint">向右滑动填充拼图</span>
              </div>
            </div>
          </div>
        </view>
      </view>
    </u-overlay>
  </div>
</template>

<script>
function sum(x, y) {
  return x + y;
}

function square(x) {
  return x * x;
}

export default {
  name: 'sliderVerify',
  props: {
    // 滑块弹窗是否显示
    show: {
      type: Boolean,
      default: false,
    },
    // 手机号
    mobile: {
      type: String,
      default: '',
    },
    // 验证码类型 1-用户注册 2-忘记密码 3-登录
    messageType: {
      type: String,
      default: '',
    },
    // 临时码
    tempCode: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      sliderWidth: 0, // 滑动条宽度
      sliderBtnWidth: 0, // 滑块宽度
      verifyImageWidth: 0, // 验证图宽度
      verifyImageHeight: 0, // 验证图高度
      verifyBlockWidth: 0, // 阻塞块宽度

      verifyData: {}, // 验证码数据
      pathList: [], // 路径数组

      blockLeft: 0, // 阻塞块距离左侧起点位置
      blockTop: 0, // 阻塞块距离顶部起点位置
      originX: 0, // 移动的X轴坐标
      originY: 0, // 移动的Y轴做坐标
      dragDistanceList: [], // 拖动距离数组
      sliderButtonLeft: 0, // 滑块按钮距离左侧起点位置
      isMouseDown: false, // 鼠标按下状态
      isLoading: true, // 图片加载提示，防止图片没加载完就开始验证
      timestamp: null, // 时间戳，计算滑动时长
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    /* 初始化*/
    async init() {
      this.getElWidth('slider', 'sliderWidth');
      this.getElWidth('slider-button', 'sliderBtnWidth');
      this.getElWidth('verify-image', 'verifyImageWidth');
      this.getElWidth('verify-block', 'verifyBlockWidth');
      this.getElWidth('verify-image', 'verifyImageHeight', 'height');
      this.getCaptcha();
    },
    // 获取一个目标元素的宽度
    getElWidth(elClass, dataVal, data = 'width') {
      new Promise((resolve, reject) => {
        const query = uni.createSelectorQuery();
        query
          .select('.' + elClass)
          .fields({ size: true }, res => {
            // 如果节点尚未生成，res值为null，循环调用执行
            if (!res) {
              setTimeout(() => {
                this.getElWidth(elClass, dataVal);
              }, 10);
              return;
            }
            console.log('getElWidth--' + elClass, res);
            this[dataVal] = res[data];
          })
          .exec();
      });
    },
    /* 获取验证码*/
    async getCaptcha() {
      console.log('init', this.mobile, this.tempCode);
      try {
        /**
         * 获取滑块验证码
         * mobile 手机号
         */
        const res = await uni.$petro.http('user.getVerifySlideImage.h5', {
          mobile: this.mobile,
        });
        console.log('res', res);
        if (!res?.success || !res?.data) {
          uni.showToast({ content: res.message || '获取滑块验证码失败' });
          this.getCaptcha();
          return;
        }
        this.isLoading = false;
        this.verifyData = res.data;
        this.blockTop = this.verifyImageHeight * res.data.heightRatio;
      } catch (e) {}
    },
    // 移动事件
    touchstart(event) {
      if (!this.verifyData || !this.verifyData.backgroundImage || this.isLoading) {
        return;
      }

      this.originX = event.changedTouches[0].pageX;
      this.originY = event.changedTouches[0].pageY;
      this.isMouseDown = true;
      this.timestamp = +new Date();
      this.pathList.push({
        x: 0,
        y: this.blockTop,
        xd: 0,
        yd: this.verifyData.heightRatio,
        time: this.getTime(),
      });
    },
    // 滑动事件
    touchmove(event) {
      if (!this.isMouseDown) {
        return;
      }

      let originX = event.changedTouches[0].pageX;
      let originY = event.changedTouches[0].pageY;

      const moveX = originX - this.originX;
      const moveY = originY - this.originY;
      if (moveX < 0 || moveX >= this.sliderWidth - this.sliderBtnWidth) {
        return;
      }
      this.sliderButtonLeft = moveX;
      this.blockLeft = (moveX / (this.sliderWidth - this.sliderBtnWidth)) * (this.verifyImageWidth - this.verifyBlockWidth);
      this.dragDistanceList.push(moveY);
    },
    // 滑动结束事件
    touchend(event) {
      if (!this.isMouseDown) {
        return;
      }

      this.isMouseDown = false;
      let originX = event.changedTouches[0].pageX;
      if (originX === this.originX) {
        return;
      }
      // 开始校验
      this.isLoading = true;
      // 滑动时长
      this.timestamp = +new Date() - this.timestamp;
      // 移动距离
      const moveLength = parseInt(this.blockLeft);
      console.log('moveLength', moveLength);

      // // 限制操作时长10S，超出判断失败
      if (this.timestamp > 10000) {
        this.onRefresh();
      }
      // 人为操作判定
      else if (!this.turingTest()) {
        this.onRefresh();
      } else {
        console.log('接口校验');
        this.pathList.push({
          x: moveLength,
          y: this.blockTop,
          xd: moveLength / this.verifyImageWidth,
          yd: this.verifyData.heightRatio,
          time: this.getTime(),
        });
        this.postVerify();
      }
    },
    // 图灵测试 人为操作判定
    turingTest() {
      const arr = this.dragDistanceList; // 拖动距离数组
      const average = arr.reduce(sum) / arr.length; // 平均值
      const deviations = arr.map(x => x - average); // 偏离值
      const stdDev = Math.sqrt(deviations.map(square).reduce(sum) / arr.length); // 标准偏差
      return average !== stdDev; // 判断是否人为操作
    },
    // 滑块接口校验
    async postVerify() {
      try {
        /**
         * messageType 验证码类型:（定义枚举类）1-用户注册 2-忘记密码 3-登录 4-换设备验证
         * mobile 用户手机号，为11位数字格式
         * reqId 滑块UUID
         * authInfo 滑块sign
         * pathList 滑块轨迹信息
         * tempCode 临时token。若messageType为3、4时必填
         */
        const res = await uni.$petro.http('user.messageCode.noLoginSend.h5', {
          messageType: this.messageType,
          mobile: this.mobile,
          reqId: this.verifyData.reqId,
          authInfo: this.verifyData.authInfo,
          pathList: this.pathList,
          tempCode: this.tempCode,
        });
        console.log('res', res);
        if (!res?.success) {
          uni.showToast({ content: res.message || '验证失败，请重试' });
          this.onRefresh();
          return;
        }
        this.isLoading = false;
        this.$emit('success');
      } catch (e) {
        // 临时码已过期
        if (e.errorCode === 'B_B15_505777' || e.errorCode === 'B_B15_100001') {
          return this.$emit('close', { isExpired: true });
        }
        this.onRefresh();
      }
    },
    /* 刷新图片验证码*/
    onRefresh() {
      this.isLoading = true;
      this.sliderButtonLeft = 0;
      this.blockLeft = 0;

      this.getCaptcha();
    },
    // 关闭弹窗
    onClose() {
      this.$emit('close');
    },
    // 获取时间
    getTime() {
      // 获取当前时间
      let now = new Date();

      // 获取年、月、日、时、分、秒
      let year = now.getFullYear();
      let month = now.getMonth() + 1;
      let day = now.getDate();
      let hours = now.getHours();
      let minutes = now.getMinutes();
      let seconds = now.getSeconds();

      // 格式化为指定格式
      month = month < 10 ? '0' + month : month;
      day = day < 10 ? '0' + day : day;
      hours = hours < 10 ? '0' + hours : hours;
      minutes = minutes < 10 ? '0' + minutes : minutes;
      seconds = seconds < 10 ? '0' + seconds : seconds;

      let formattedDate = year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + seconds;
      return formattedDate;
    },
  },
};
</script>

<style scoped lang="scss">
// 弹窗
.verify-popup {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;

  &-content {
    width: calc(100% - 192rpx);
    padding: 32rpx;
    box-sizing: border-box;
    background: #fff;
    border-radius: 16rpx;
  }
}
/* 顶部 */
.slide-header {
  width: 100%;
  margin-bottom: 32rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: space-between;

  /* 提示 */
  .slide-tips {
    font-family: PingFang SC, PingFang SC;
    font-weight: 500;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    text-align: left;
    flex: 1;
  }

  /* 刷新按钮 */
  .refresh-icon {
    width: 30rpx;
    height: 30rpx;
    display: block;
    margin-left: 26rpx;
  }
}

// 滑块主体
.slide-content {
  position: relative;
}

/*图片加载样式*/
.loading {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  z-index: 999;
  border-radius: 5rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 248rpx;
}

/*验证图片*/
.verify-image {
  width: 100%;
  height: 248rpx;
  background: #d9d9d9;
  border-radius: 12rpx;
}

/*阻塞块*/
.verify-block {
  position: absolute;
  left: 0;
  top: 0;
  width: 80rpx;
  height: 80rpx;
  margin-top: 4rpx;
}

/*滑动条*/
.slider {
  position: relative;
  width: 100%;
  height: 92rpx;
  background: #f1f4f6;
  border-radius: 46rpx;
  line-height: 92rpx;
  margin-top: 32rpx;
  text-align: center;
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 32rpx;
  color: #bfbfbf;
}

/*滑动按钮*/
.slider-button {
  position: absolute;
  top: 0;
  left: 0;
  width: 92rpx;
  height: 92rpx;
  border-radius: 50%;
  box-sizing: border-box;
  background-color: #fa1919;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;

  /*鼠标悬浮时的按钮样式*/
  &:hover {
    background-color: #ee0000;
  }

  &-icon {
    width: 45rpx;
    height: 25rpx;
  }
}
</style>
