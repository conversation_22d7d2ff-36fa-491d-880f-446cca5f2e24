<template>
  <view class="page-product">
    <petro-layout ref="layout">
      <zyzx-page-product :isOpened="isOpened"></zyzx-page-product>
    </petro-layout>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      isOpened: true,
    };
  },
  onLoad(query) {
    console.log('onLoad', query);
    this.isOpened = !!query?.isOpened;
  },
  methods: {},
};
</script>

<style lang="scss" scoped></style>
