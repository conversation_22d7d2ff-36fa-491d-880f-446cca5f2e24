<template>
  <view class="page-roles">
    <u-navbar :title="isInit ? '' : '切换身份'" :autoBack="true" :placeholder="true" :bgColor="'#fff'"></u-navbar>

    <petro-layout ref="layout">
      <zyzx-page-roles v-if="isInit !== null" :data="isInit ? roleList : []" @onSelected="onSelected"></zyzx-page-roles>
    </petro-layout>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      isInit: null,
      roleList: [
        {
          id: 1,
          code: 1,
          name: '我是车队管理员',
          shortName: '车队',
          remark: '昆仑e享卡、车辆及司机信息管理',
          enterpriseName: '',
        },
        {
          id: 2,
          code: 2,
          name: '我是车队司机',
          shortName: '司机',
          remark: '使用昆仑e享卡进行油费管理',
          enterpriseName: '',
        },
        // {
        //   id: 3,
        //   code: 3,
        //   name: '礼品卡券管理员',
        //   shortName: '礼品卡',
        //   remark: '昆仑e享卡及礼品卡券信息管理',
        // },
      ],
      businessList: [], // 车队业务企业列表
      driverList: [], // 司机企业列表
      giftList: [], // 礼品卡业务企业列表
    };
  },
  onLoad(query) {
    this.isInit = Number(query?.isInit) !== 0;
    if (this.isInit) {
      this.getUserEnterprise();
    }
  },
  methods: {
    async onSelected(v) {
      console.log(v);

      let activatedList = []; // 已激活/冻结列表
      if (v.code === 2) {
        // 车队司机
        if (!this.driverList.length) {
          // 无司机列表
          this.onToNone();
          return;
        }

        activatedList = this.driverList.filter(item => [1, 2].includes(item.accountStatus));
        // 无已激活/冻结司机列表
        if (!activatedList.length) {
          this.onToSetting();
          return;
        }

        await this.switchCompanyAndRole(activatedList[0].enterpriseNo, 4, 10, activatedList[0].businessNo);
        this.toDriver(activatedList[0].businessNo);
        return;
      }

      if (v.code === 3) {
        // 礼品卡业务企业列表
        if (!this.giftList.length) {
          // 无礼品卡业务企业列表
          this.onToNone();
          return;
        }

        activatedList = this.giftList.filter(item => [1, 2].includes(item.accountStatus));
        // 无已激活/冻结礼品卡业务企业列表
        if (!activatedList.length) {
          this.onToSetting();
          return;
        }

        this.onToGift();
        return;
      }

      // 车队业务联系人
      activatedList = this.businessList.filter(item => [1, 2].includes(item.accountStatus));
      if (!this.businessList.length) {
        // 无车队业务企业列表
        uni.$petro.route({ url: '/pages/business-info/business-info', params: { businessMark: 1 }, type: 'navigateTo' });
        return;
      }

      // 无已激活/冻结车队业务企业列表
      if (!activatedList.length) {
        this.onToSetting();
        return;
      }

      await this.switchCompanyAndRole(activatedList[0].enterpriseNo, 2, 10, activatedList[0].businessNo);
      this.toManager(activatedList[0].businessNo);
    },
    // 跳转无邀请
    onToNone() {
      uni.$petro.route({ url: '/pages/none/none', type: 'navigateTo' });
    },
    // 跳转激活页面
    onToSetting() {
      uni.$petro.route({ url: '/pages/todo/todo', params: { isInit: 1 }, type: 'navigateTo' });
    },
    // 跳转礼品卡
    onToGift() {
      uni.$petro.route({ url: '/pages/gift-code/gift-code', type: 'reLaunch' });
    },
    // 获取车队业务类型
    async getFleetType(businessNo) {
      try {
        const res = await uni.$petro.http('user.business.getFleetType.h5', {
          businessNo,
        });
        console.log('getFleetType', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = Object.assign(this.saveInfo, res.data);
      } catch (e) {
        console.log(e);
      }
    },
    // 跳转管理端
    async toManager(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-10' });
      }, 500);
    },
    // 跳转司机端
    async toDriver(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '4-10' });
      }, 500);
    },
    // 获取获取已、未开通企业列表
    async getUserEnterprise() {
      try {
        const res = await uni.$petro.http('user.business.queryEnterpriseBusinessList.h5', {
          queryType: 5,
        });
        console.log('res', res);

        let managerList = (res?.data || []).filter(item => item.role === 2 && item.accountStatus !== 3); // 未注销企业列表
        this.driverList = (res?.data || []).filter(item => item.role === 4 && item.accountStatus !== 3); // 未注销司机列表

        this.businessList = managerList.filter(item => item.businessType === 10); // 车队列表
        this.giftList = managerList.filter(item => item.businessType === 11); // 礼品卡列表
      } catch (e) {
        console.log(e);
      } finally {
      }
    },
    // 切换企业和角色
    async switchCompanyAndRole(enterpriseNo, roleType, roleBusinessType, businessNo) {
      try {
        const res = await uni.$petro.http('user.companySwitch.h5', {
          orgCode: enterpriseNo,
          type: 1,
          roleType,
          roleBusinessType,
          businessNo,
        });
        console.log('switchCompanyAndRole', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = res.data;
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-roles {
  box-sizing: border-box;
  width: 100%;
  min-height: 100vh;

  .header {
    width: 100%;
    padding: 252rpx 32rpx 26rpx;
    box-sizing: border-box;

    &-bg {
      width: 100%;
      height: 372rpx;
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
    }

    &-title {
      font-weight: 500;
      font-size: 36rpx;
      color: #333333;
      line-height: 44rpx;
    }

    &-subTitle {
      margin-top: 4rpx;
      font-weight: 500;
      font-size: 24rpx;
      color: #666666;
      line-height: 44rpx;
    }
  }
}
</style>
