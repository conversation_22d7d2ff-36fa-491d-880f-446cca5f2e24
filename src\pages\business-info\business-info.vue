<template>
  <div class="page-business-info">
    <petro-layout ref="layout">
      <zyzx-page-business-open :businessMark="businessMark"></zyzx-page-business-open>
    </petro-layout>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      businessMark: 1,
    };
  },
  onLoad(query) {
    this.businessMark = Number(query?.businessMark);
  },
  methods: {},
};
</script>

<style scoped lang="scss"></style>
