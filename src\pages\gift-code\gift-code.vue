<template>
  <div class="page-gift">
    <view class="container">
      <div v-show="tabBarValue === 'gift-code'">
        <gift-code v-if="tabBarCache.includes('gift-code')" :page-show="pageShow"></gift-code>
      </div>
      <div v-if="tabBarValue === 'wallet'">
        <wallet v-if="tabBarCache.includes('wallet')"></wallet>
      </div>
      <div v-show="tabBarValue === 'order'">
        <order v-if="tabBarCache.includes('order')"></order>
      </div>
      <div v-show="tabBarValue === 'mine'">
        <mine v-if="tabBarCache.includes('mine')"></mine>
      </div>
    </view>

    <p-tabbar></p-tabbar>

    <root-portal :enable="true">
      <u-modal :show="passwordShow" title="温馨提示" confirmColor="#fa1919" width="580rpx" @confirm="toSetting">
        <view class="slot-content"> 未设置支付密码，请设置支付密码 </view>
      </u-modal>
    </root-portal>
  </div>
</template>

<script>
import GiftCode from '@/pages/gift-code/pages/gift-code/gift-code.vue';
import Wallet from '@/pages/wallet/wallet.vue';
import Order from '@/pages/order/order.vue';
import Mine from '@/pages/mine/mine.vue';

import { mapState } from 'vuex';
import PTabbar from '@/components/p-tabbar/p-tabbar.vue';

export default {
  components: { PTabbar, GiftCode, Wallet, Order, Mine },
  data() {
    return {
      debugList: [
        {
          label: 'demo页',
          value: 'demo',
        },
      ],
      pageShow: true,
      passwordShow: false,
    };
  },
  computed: {
    ...mapState({
      tabBarValue: state => state?.tabBar?.tabBarValue,
      tabBarCache: state => state?.tabBar?.tabBarCache,
      walletInfo: state => state.account?.walletInfo,
    }),
    passwordShow() {
      return !this.walletInfo?.managePassword;
    },
  },
  async beforeCreate() {
    console.log('beforeCreate');
    const app = getApp();
    await app.hookInit();
  },
  async created() {
    console.log('created');
  },
  async onLoad(query) {
    console.log('onLoad', query);
  },
  async onShow() {
    this.pageShow = true;
  },
  onHide() {
    this.pageShow = false;
  },
  onUnload() {},
  mounted() {},
  methods: {
    // 调试
    onDebug({ value }) {
      if (value === 'demo') {
        uni.$petro.route('/pages/demo/demo', { test: 1 });
      }
    },
    toSetting() {
      uni.$petro.route({ url: '/pages/wallet/pages/face-recognition-page', type: 'navigateTo' });
    },
  },
};
</script>

<style scoped lang="scss">
.page-gift {
  background: #f0f1f5;
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  overflow: hidden;

  > div {
    height: 100%;
    overflow: auto;
  }
}
</style>
