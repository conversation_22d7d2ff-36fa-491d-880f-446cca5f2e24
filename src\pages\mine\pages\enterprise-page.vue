<template>
  <div class="enterprise-page">
    <u-navbar title="企业信息" :autoBack="true" :placeholder="true"></u-navbar>

    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-enterprise></zyzx-page-enterprise>
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import zyzxPageEnterprise from '@/components/zyzx-page-enterprise/zyzx-page-enterprise.vue';

export default {
  name: 'enterprise-page',
  // components: { zyzxPageEnterprise },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.enterprise-page {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  padding-top: 24rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
