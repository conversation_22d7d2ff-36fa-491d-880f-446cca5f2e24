<template>
  <div class="personal-info-page">
    <u-navbar title="个人信息" :autoBack="true" :placeholder="true"></u-navbar>
    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-personal></zyzx-page-personal>
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import zyzxPagePersonal from '@/components/zyzx-page-personal/zyzx-page-personal';

export default {
  name: 'personal-info-page',
  // components: { zyzxPagePersonal },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.personal-info-page {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
