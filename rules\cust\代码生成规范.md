# AI辅助开发指南 - 用户小程序

## 技术环境

- **框架**: UniApp + Vue 2 (Options API)
- **UI库**: uView UI 2.0.36 + @petro-soti/foudation-zyzx
- **语言**: JavaScript (不使用TypeScript)
- **状态管理**: Vuex 3.x
- **样式**: SCSS
- **平台**: 微信小程序、支付宝小程序、mPaaS小程序

## 核心约束

1. **必须使用Vue 2 Options API**，不使用Vue 3或Composition API
2. **HTTP请求必须使用`uni.$petro.http()`**
3. **组件优先级**: uView UI > petro/zyzx前缀组件 > 自定义组件
4. **状态管理只用Vuex**，不使用其他状态库
5. **样式必须使用SCSS + scoped**

## 项目结构

```
src/
├── components/       # 组件目录(使用p-前缀)
├── pages/           # 页面目录
├── services/
│   ├── http/        # API接口
│   └── store/       # Vuex状态
└── utils/
    └── mock.js      # Mock数据
```

## API开发规范

### 标准API函数结构

```javascript
/**
 * 功能描述
 * @param {Object} data - 请求参数
 * @param {Object} config - 请求配置
 * @returns {Promise} 请求结果
 */
export function apiFunction(data, config) {
  return uni.$petro.http('module.action.h5', data, {
    ...config,
    mockResponse: mock.apiFunction
  });
}
```

### 必要的Mock数据

```javascript
// utils/mock.js
export default {
  apiFunction: {
    success: true,
    data: {
      // 模拟数据结构
    }
  }
}
```

## 组件开发规范

### 标准组件结构

```vue
<template>
  <view class="p-component-name">
    <!-- 使用uView组件 -->
    <u-button type="primary">按钮</u-button>
  </view>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
  name: 'PComponentName',
  
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      loading: false
    };
  },
  
  computed: {
    ...mapState('module', ['stateData'])
  },
  
  methods: {
    ...mapActions('module', ['actionName']),
    
    async handleAction() {
      try {
        this.loading = true;
        const { success, data } = await this.actionName();
        // 处理结果
      } catch (error) {
        console.error(error);
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.p-component-name {
  // SCSS样式
}
</style>
```

## 状态管理规范

### Vuex模块结构

```javascript
export default {
  namespaced: true,
  
  state: {
    data: null,
    loading: false,
    error: null
  },
  
  mutations: {
    SET_DATA(state, data) {
      state.data = data;
    },
    SET_LOADING(state, status) {
      state.loading = status;
    },
    SET_ERROR(state, error) {
      state.error = error;
    }
  },
  
  actions: {
    async fetchData({ commit }, params) {
      try {
        commit('SET_LOADING', true);
        commit('SET_ERROR', null);
        
        const { success, data } = await uni.$petro.http('module.getData.h5', params);
        
        if (success) {
          commit('SET_DATA', data);
        }
        
        return { success, data };
      } catch (error) {
        commit('SET_ERROR', error.message);
        throw error;
      } finally {
        commit('SET_LOADING', false);
      }
    }
  },
  
  getters: {
    hasData: state => !!state.data
  }
};
```

## 开发检查清单

- [ ] 使用Vue 2 Options API
- [ ] 使用uni.$petro.http()进行API调用
- [ ] 组件使用p-前缀命名
- [ ] 使用uView UI组件
- [ ] 使用Vuex进行状态管理
- [ ] 样式使用SCSS + scoped
- [ ] API配置了Mock数据
- [ ] 遵循项目目录结构

## 禁止事项

- 不使用Vue 3或Composition API
- 不使用除uni.$petro.http()外的HTTP请求方法
- 不修改package.json或安装新依赖
- 不在组件中硬编码测试数据
- 不使用TypeScript

## 交互语言

所有代码注释和文档说明必须使用简体中文。