<template>
  <div class="page-wallet">
    <u-navbar title="我的钱包" :autoBack="true" :placeholder="true" @rightClick="handleToChange">
      <div class="u-nav-slot" slot="right"> 账户设置 </div>
    </u-navbar>
    <petro-layout ref="layout" :petroKeyboard="true">
      <div class="wallet">
        <!-- 钱包基础信息  -->
        <zyzx-page-wallet :account-info="false" :card-pull-load="true" :business-info="false" :background-color="'#f5f5f5'" />
        <!-- 账单列表 -->
        <div class="bill">
          <zyzx-page-bill
            ref="zyzxPageBillRef"
            :isPullDown="true"
            @pullDownClick="pullDownClick"
            :enterpriseAccountNo="walletInfo.enterpriseAccountNo"
            :mainAccountNo="walletInfo.mainAccountNo"
            :isShowBar="true"
          />
        </div>
      </div>
    </petro-layout>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'wallet',
  data() {
    return {};
  },
  computed: {
    ...mapState({
      walletInfo: state => state?.account?.walletInfo,
    }),
  },
  onShow() {},
  created() {},
  mounted() {
    this.$nextTick(async () => {
      console.log('2222');
      await this.pullDownClick();
      this.$refs.zyzxPageBillRef.getBillList(true);
    });
  },
  methods: {
    async pullDownClick() {
      await this.$store.dispatch('getWalletInfo', {
        staffRole: 2,
        refresh: true,
      });
    },
    handleToChange() {
      //uni.$petro.route({ url: '/pages/wallet/pages/face-recognition-page', type: 'navigateTo' });
      console.log(this.walletInfo,'跳转到设置页面');
      // 跳转到设置页面
      uni.$petro.route(
        { 
          url: '/pages/wallet/pages/set-up-page', 
          type: 'navigateTo',
          params: {  mainAccountNo: this.walletInfo.mainAccountNo}// 传递参数 车牌卡设置
        },
        true,
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.page-wallet {
  width: 100%;
  height: calc(100vh - 100rpx);
  background: #f0f1f5;
  display: flex;
  flex-direction: column;
}

.u-nav-slot {
  display: flex;
  /* #ifdef MP-WEIXIN || MP-ALIPAY */
  margin-right: 160rpx;
  /* #endif */
  color: #666666;
  font-size: 28rpx;
}

.container {
  flex: 1;
  overflow: scroll;
}

.wallet {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.bill {
  flex: 1;
  overflow: scroll;
}
</style>
