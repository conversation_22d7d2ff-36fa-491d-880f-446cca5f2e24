<template>
  <div class="page-help">
    <u-navbar title="开通业务常见问题" :autoBack="true" :placeholder="true" :bgColor="'transparent'"> </u-navbar>

    <view class="black"></view>
    <view class="container">
      <view class="question" v-for="(item, index) in questions" :key="item.title">
        <view class="question-title" @click="onChange(index)">
          <view class="question-title-value">{{ item.title }}</view>
          <img src="@/static/icon-up.png" :class="['question-title-icon', { up: currentIndex === index }]" />
        </view>
        <view class="question-answer" v-show="currentIndex === index">{{ item.value }}</view>
      </view>
    </view>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      questions: [
        {
          title: '1.如何开通业务？',
          value:
            '准备好相关资质后可通过中油智行App、对外合作伙伴门户、网点柜台三个渠道进行业务开通。App开通业务仅支持通过营业执照进行开通，如果您是其他组织类型，请到网点柜台进行开通。',
        },
        {
          title: '2.开通业务需要准备的资料有哪些？',
          value:
            '单位开通业务手续：单位营业执照副本加盖公章、法人身份证件、经办人身份证件、业务准入授权书加盖公章、业务合同（如有）。持有其他资质证件，如统一信用代码证书、事业单位法人证书等，请到网点柜台进行开通。',
        },
        {
          title: '3.开通业务的审核时效是多久？',
          value: '平台进行资质审核约1-3个工作日，如需加急处理请联系您的业务受理网点。',
        },
        {
          title: '4.开通业务后，还可以增加业务类型吗？',
          value: '业务开通后，可在中油智行App内、对外合作伙伴门户、网点柜台三个渠道进行其他业务开通。',
        },
        {
          title: '5.没有营业执照可以入驻吗？',
          value: '如持有统一信用代码证书、事业单位法人证书等其他资质证件，请到网点柜台进行开通。',
        },
        {
          title: '6.没有法人身份证可以入驻吗？',
          value: '没有法人证件可出具业务授权书并加盖公章，到网点柜台进行开通。',
        },
      ],
      currentIndex: -1,
    };
  },
  computed: {},
  onLoad(query) {},
  methods: {
    // 显示问题答案
    onChange(index) {
      this.currentIndex = this.currentIndex === index ? -1 : index;
    },
  },
};
</script>

<style scoped lang="scss">
.page-help {
  background: #ffffff;
  padding-bottom: 36rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.black {
  height: 24rpx;
  background: #f0f1f5;
}

.container {
  width: 100%;
  padding: 0 32rpx;
  box-sizing: border-box;
}

.question {
  padding: 30rpx 0;
  box-shadow: 0px 1rpx 0px 0px #eeeeee;
  box-sizing: border-box;

  &-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 44rpx;

    &-value {
      flex: 1;
      font-family: PingFang SC, PingFang SC;
      font-weight: 500;
      font-size: 32rpx;
      color: #333333;
      line-height: 44rpx;
    }

    &-icon {
      width: 32rpx;
      height: 32rpx;
      margin-right: 22rpx;
      transform: rotate(180deg);
    }

    .up {
      transform: rotate(0deg);
    }
  }

  &-answer {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 28rpx;
    color: #666666;
    line-height: 52rpx;
    margin-top: 20rpx;
  }
}
</style>
