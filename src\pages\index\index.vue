<template>
  <view class="page-index">
    <view class="loading" v-show="isLoading">
      <img class="loading-img" src="@/static/icon-loading.gif" />
      <view class="loading-value">正在努力加载中...</view>
    </view>
    <!-- 协议页面 -->
    <p-agreement @agree="onAgree" v-if="!isAgree"></p-agreement>

    <!-- 登录/注册页面 -->
    <p-login :locationInfo="locationInfo" @success="inputSuccess" v-else></p-login>

    <!-- 滑块验证码弹窗 -->
    <div v-if="sliderVisable">
      <p-slider-verify
        :show="sliderVisable"
        :mobile="mobile"
        :messageType="messageType"
        :tempCode="tempCode"
        @success="onSliderVerifySuccess"
        @close="onSliderClose"
      />
    </div>
  </view>
</template>

<script>
import pAgreement from '@/components/p-agreement/p-agreement.vue';
import pLogin from '@/components/p-login-password/p-login-password.vue';

export default {
  components: { pAgreement, pLogin },
  data() {
    return {
      isLoading: true, // 是否加载中
      isAgree: true, // 是否同意协议
      saveInfo: {}, // 存储信息
      roleList: [], // 当前登录账号角色列表
      role: {}, // 当前登录账号默认角色
      sliderVisable: false, // 滑块验证是否显示
      mobile: '', // 手机号
      messageType: '3', // 1-用户注册 2-忘记密码 3-登录 4-换设备验证
      tempCode: '', // 临时验证码
      authStatus: 0, // 换设备校验状态:0-通过 1-短信验证 2-人脸验证
      locationInfo: {}, // 位置信息,
      debugList: [
        {
          label: 'demo页',
          value: 'demo',
        },
      ],
    };
  },
  async mounted() {
    this.init();
  },
  methods: {
    // 调试
    onDebug({ value }) {
      if (value === 'demo') {
        uni.$petro.route('/pages/demo/demo', { test: 1 });
      }
    },
    // 初始化
    async init() {
      try {
        const res = await uni.$petro.getTokenInfo();
        console.log('getTokenInfo', res);
        this.saveInfo = res || {};
        // 是否已登录
        if (!!res?.gsmsToken) {
          await this.getUserRole();
          return;
        }

        // 是否同意协议
        this.isAgree = !!res?.isAgreePrivacy;
        this.isLoading = false;
      } catch (e) {
        this.isAgree = false;
        this.isLoading = false;
      }
    },
    // 同意协议
    onAgree() {
      this.isAgree = true;
    },
    // 获取用户角色
    async getUserRole(reload = false) {
      try {
        const res = await uni.$petro.http('user.role.list.h5', {}, { showLoading: false });
        console.log('res', res);

        // 获取角色列表--过滤掉企业管理员
        this.roleList = (res?.data || []).filter(item => item.code !== '1');

        // 判断是否有默认角色
        this.role = this.roleList.find(item => item.defaultFlag === 1) || {};

        if (reload) return;

        // 7-7正式供应商
        if (this.role?.code === '7-7') {
          setTimeout(() => {
            uni.$petro.switchTinyApp(this.role);
          }, 500);
          return;
        }

        await this.getUserEnterprise();
      } catch (e) {
        this.isLoading = false;
      }
    },
    // 获取获取已、未开通企业列表
    async getUserEnterprise() {
      try {
        const res = await uni.$petro.http(
          'user.business.queryEnterpriseBusinessList.h5',
          {
            queryType: 5,
          },
          { showLoading: false },
        );
        console.log('res', res);

        //let managerList = (res?.data || []).filter(item => item.accountStatus === 3); // 未注销企业列表

        let managerList = (res?.data || []).filter(item => item.role === 2 && item.accountStatus !== 3); // 未注销企业列表
        let driverList = (res?.data || []).filter(item => item.role === 4 && item.accountStatus !== 3); // 未注销司机列表

        // 2-11礼品卡券管理员
        if (this.role?.code === '2-11') {
          let giftInfo = managerList.find(item => item.businessNo == this.role.businessNo && item.businessType === 11);
          if (giftInfo) {
            return [1, 2].includes(giftInfo.accountStatus) ? this.toGift() : this.toSetting();
          }
        }

        // 2-6非油直销业务联系人
        if (this.role?.code === '2-6') {
          let directsaleInfo = managerList.find(item => item.businessNo == this.role.businessNo && item.businessType === 6);
          if (directsaleInfo) {
            return [1, 2].includes(directsaleInfo.accountStatus) ? this.toDirectsale() : this.toSetting();
          }
        }

        let managerActivatedList = managerList.filter(item => [1, 2].includes(item.accountStatus) && item.businessType === 10); // 已激活/冻结车队业务联系人列表
        let driverActivatedList = driverList.filter(item => [1, 2].includes(item.accountStatus)); // 已激活/冻结司机列表

        // 已激活/冻结车队业务联系人
        let managerInfo = managerActivatedList.find(
          item => item.businessNo === this.role?.businessNo && item.enterpriseNo === this.saveInfo?.orgCode && this.role?.code === '2-10',
        );
        if (managerInfo) return this.toManager(managerInfo.businessNo);

        // 已激活/冻结司机
        let driverInfo = driverActivatedList.find(
          item => item.businessNo === this.role?.businessNo && item.enterpriseNo === this.saveInfo?.orgCode && this.role?.code === '4-10',
        );
        if (driverInfo) return this.toDriver(driverInfo.businessNo);

        if (managerActivatedList?.length) {
          // 已激活/冻结车队业务联系人--切换登录信息
          await this.switchCompanyAndRole(managerActivatedList[0].enterpriseNo, 2, 10, managerActivatedList[0].businessNo);
          this.toManager(managerActivatedList[0].businessNo);
        } else if (driverActivatedList?.length) {
          // 已激活/冻结司机--切换登录信息
          await this.switchCompanyAndRole(driverActivatedList[0].enterpriseNo, 4, 10, driverActivatedList[0].businessNo);
          this.toDriver(driverActivatedList[0].businessNo);
        } else if (managerList.length || driverList.length) {
          // 有待激活企业
          this.toSetting();
        } else {
          // 无待激活企业--跳转产品页
          this.toProduct();
        }
      } catch (e) {
        this.isLoading = false;
        this.toProduct();
      } finally {
        setTimeout(() => {
          this.isLoading = false;
        }, 800);
      }
    },
    // 切换企业和角色
    async switchCompanyAndRole(enterpriseNo, roleType, roleBusinessType, businessNo) {
      try {
        const res = await uni.$petro.http('user.companySwitch.h5', {
          orgCode: enterpriseNo,
          type: 1,
          roleType,
          roleBusinessType,
          businessNo,
        });
        console.log('switchCompanyAndRole', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = res.data;
      } catch (e) {
        console.log(e);
      }
    },
    // 获取车队业务类型
    async getFleetType(businessNo) {
      try {
        const res = await uni.$petro.http('user.business.getFleetType.h5', {
          businessNo,
        });
        console.log('getFleetType', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = Object.assign(this.saveInfo, res.data);
      } catch (e) {
        console.log(e);
      }
    },
    // 跳转管理端
    async toManager(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-10' });
      }, 500);
    },
    // 跳转司机端
    async toDriver(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '4-10' });
      }, 500);
    },
    // 跳转礼品卡
    toGift() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/gift-code/gift-code', type: 'redirectTo' });
      }, 500);
    },
    // 跳转非油直销
    toDirectsale() {
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-6' });
      }, 500);
    },
    // 跳转激活页面
    toSetting() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/todo/todo', params: { isInit: 1 }, type: 'navigateTo' });
      }, 500);
    },
    // 跳转业务选择页面
    toProduct() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/product/product', type: 'navigateTo', params: { isFace: true } });
      }, 500);
    },
    // 跳转验证码页面
    toVerification() {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/verification/verification',
          params: { mobile: this.mobile, messageType: this.messageType, tempCode: this.tempCode },
          type: 'navigateTo',
        });
      }, 500);
    },
    // 跳转换设备验证页面
    toVerify() {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/identity-verification/identity-verification',
          params: {
            mobile: this.mobile,
            messageType: this.messageType,
            tempCode: this.tempCode,
            authStatus: this.authStatus,
            locationInfo: encodeURIComponent(JSON.stringify(this.locationInfo)),
          },
          type: 'navigateTo',
        });
      }, 500);
    },
    // 用户输入校验通过
    inputSuccess({ authStatus, mobile, messageType, tempCode }) {
      this.authStatus = authStatus; // 换设备校验状态:0-通过 1-短信验证 2-人脸验证
      this.mobile = mobile;
      this.messageType = messageType;
      this.tempCode = tempCode;
      if (authStatus === 0) {
        this.isLoading = true;
        this.init();
        return;
      } else if (authStatus === 1 || authStatus === 2) {
        this.toVerify();
        return;
      }
      this.sliderVisable = true;
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisable = false;
      uni.showToast({ content: '短信验证码已发送' });
      this.toVerification();
    },
    // 滑块验证关闭
    onSliderClose() {
      this.sliderVisable = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.loading {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  background: #ffffff;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 998;

  &-img {
    width: 560rpx;
    height: 560rpx;
  }

  &-value {
    margin-top: 48rpx;
    font-weight: 400;
    font-size: 24rpx;
    color: #999999;
    line-height: 44rpx;
  }
}
</style>
