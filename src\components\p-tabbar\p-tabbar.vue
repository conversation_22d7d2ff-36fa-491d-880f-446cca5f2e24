<template>
  <div>
    <u-tabbar
      :value="tabBarValue"
      @change="tabBarChange($event)"
      :fixed="true"
      :placeholder="true"
      :safeAreaInsetBottom="true"
      inactiveColor="#767678"
      activeColor="#FA1919"
    >
      <u-tabbar-item :name="item.value" :text="item.name" v-for="(item, i) in tabBarList" :key="i">
        <image class="tab-item-slot-icon" slot="inactive-icon" :src="item.icon"></image>
        <image class="tab-item-slot-icon" slot="active-icon" :src="item.iconActive"></image>
      </u-tabbar-item>
    </u-tabbar>
  </div>
</template>

<script>
import { mapState } from 'vuex';

export default {
  name: 'p-tabbar',
  props: {},
  data() {
    return {};
  },
  mounted() {},
  computed: {
    ...mapState({
      tabBarValue: state => state?.tabBar?.tabBarValue,
      tabBarCache: state => state?.tabBar?.tabBarCache,
      tabBarList: state => state?.tabBar?.tabBarList,
    }),
  },
  methods: {
    // 切换底部导航栏的回调函数
    tabBarChange(val) {
      this.$store.commit('switchTab', val);
    },
  },
};
</script>

<style scoped lang="scss">
.tab-item-slot-icon {
  width: 50rpx;
  height: 50rpx;
}
</style>
