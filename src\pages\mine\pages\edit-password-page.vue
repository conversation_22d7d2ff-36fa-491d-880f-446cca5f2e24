<template>
  <div class="edit-password-page">
    <u-navbar title="修改登录密码" :autoBack="true" :placeholder="true"></u-navbar>

    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-password></zyzx-page-password>
      </petro-layout>
    </div>
  </div>
</template>

<script>
// import zyzxPagePassword from '@/components/zyzx-page-password/zyzx-page-password.vue';

export default {
  name: 'edit-password-page',
  // components: { zyzxPagePassword },
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {},
};
</script>

<style scoped lang="scss">
.edit-password-page {
  width: 100%;
  height: 100vh;
  background: #f0f1f5;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
