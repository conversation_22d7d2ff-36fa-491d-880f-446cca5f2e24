<template>
  <view class="page-todo">
    <u-navbar :title="isInit ? ' ' : '待办事项'" :autoBack="true" :placeholder="true" :bgColor="'transparent'"></u-navbar>
    <div class="page-content">
      <petro-layout ref="layout">
        <zyzx-page-unit-setting :isInit="isInit" :isRelate="isRelate" @success="onActivated" v-if="isShow"></zyzx-page-unit-setting>
      </petro-layout>
    </div>
  </view>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      isInit: false,
      isShow: false,
      isRelate: false,
    };
  },
  onLoad(query) {
    console.log('onLoad', query);
    this.isInit = Number(query?.isInit) === 1;
    this.isRelate = Number(query?.isRelate) === 1;
    this.isShow = true;
  },
  methods: {
    async onActivated() {
      const app = getApp();
      await app.hookInit();
      // uni.$petro.route({
      //   type: 'back',
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-todo {
  height: 100vh;
  background: #fff;

  .page-content {
    position: absolute;
    top: 0;
    left: 0;
  }
}
</style>
