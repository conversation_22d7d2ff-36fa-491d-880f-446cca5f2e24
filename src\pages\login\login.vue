<template>
  <div class="page-login">
    <u-navbar title="Demo" :autoBack="true" :placeholder="false" :bgColor="'transparent'"> </u-navbar>

    <img class="cover" src="http://picsum.photos/375/200?r=1" alt="" />

    <view class="container">
      <image class="logo" mode="widthFix" src="https://oss-alipay-prd-soti.oss-cn-beijing.aliyuncs.com/v2.0/images/login/logo.png"></image>
      <!-- #ifdef MP-ALIPAY -->
      <button class="btn-petro-t" open-type="getPhoneNumber" @getphonenumber="onGetAuthorize">支付宝授权登录</button>
      <!-- #endif -->
    </view>

    <!-- 一键登录组件 -->
    <!-- #ifdef MP-ALIPAY -->
    <petro-login-zfb @onLoginSuccess="onLoginSuccess" ref="loginRef"></petro-login-zfb>
    <!-- #endif -->
  </div>
</template>

<script>
import appMixin from './platform/login-app';
import wxMixin from './platform/login-wx';
import zfbMixin from './platform/login-zfb';

export default {
  mixins: [appMixin, wxMixin, zfbMixin],
  components: {},
  data() {
    return {
      title: '登录', // 标题
      type: 0, // 1 登录 2 注册
      phone: '', // 一键登录手机号
      userInfo: null,
      encUserInfo: null,
    };
  },
  computed: {},
  onLoad(query) {
    console.log('login onLoad', query);
    try {
      if (query.data) query.data = JSON.parse(query.data);
      console.log('parse query.data', query.data);
    } catch (err) {
      console.error(err);
    }
  },
  methods: {
    // 授权一键登录/获取手机号
    async onGetAuthorize() {
      if (this.$refs.loginRef) this.$refs.loginRef.openDialog();
    },
    async onLoginSuccess({ type }) {
      console.log('登录成功~', type);
      if (type === 'v3') {
        uni.$petro.init();
        uni.$petro.reLaunchHome();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-login {
  background: #f5f5f5;
}

.cover {
  width: 100%;
}

.logo {
  width: 100px;
  margin: 12px auto;
  display: inherit;
}

.container {
  padding: 10px;
}
</style>
