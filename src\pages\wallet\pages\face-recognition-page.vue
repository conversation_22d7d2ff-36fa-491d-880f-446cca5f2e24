<template>
  <view class="face-recognition-page">
    <u-navbar title="身份校验" :autoBack="true" :placeholder="true"> </u-navbar>

    <!-- 账单列表 -->
    <div class="container">
      <petro-layout ref="layout" :petroKeyboard="true">
        <zyzx-page-face-recognition />
      </petro-layout>
    </div>
  </view>
</template>

<script>
export default {
  name: 'face-recognition-page',
  data() {
    return {};
  },
  created() {},
  mounted() {},
  methods: {
    /**
     * 跳转到帮助页面
     */
    onToHelp() {
      uni.$petro.route({ url: '/pages/help/help', type: 'navigateTo' });
    },
  },
};
</script>

<style scoped lang="scss">
.face-recognition-page {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  background: #f0f1f5;
  padding-top: 24rpx;
  display: flex;
  flex-direction: column;
}
.container {
  flex: 1;
  overflow: scroll;
}
</style>
