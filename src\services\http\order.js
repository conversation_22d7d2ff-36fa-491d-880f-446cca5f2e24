// 获取订单列表
export function getGiftCardOrderListApi(data, config) {
  const mockData = {
    success: true,
    data: {
      pageNum: 1,
      pageSize: 20,
      pageSum: 1,
      totalRows: 1,
      rows: [
        {
          orderNo: '2401251539040007038473013',
          transactionPlaceName: '天津销售武清分公司高王路加油站',
          orderStatus: 3,
          realAmount: 990,
          createTime: '2024-07-10 15:30:34',
          orderTime: '2024-07-10 15:34:34',
          sourceChannelNo: 42,
          cardTypeNo: 12,
          giftCardItems: [
            {
              cardStyleName: '100元昆仑e享卡',
              faceValue: '99.0',
              cardNum: 10,
            },
          ],
        },
        {
          orderNo: '2401251539040007038473016',
          transactionPlaceName: '天津销售武清分公司高王路加油站',
          orderStatus: 3,
          realAmount: 1980,
          createTime: '2024-07-10 15:30:34',
          orderTime: '2024-07-10 15:34:34',
          sourceChannelNo: 42,
          cardTypeNo: 13,
          giftCardItems: [
            {
              cardStyleName: '100元昆仑e享卡',
              faceValue: '99.0',
              cardNum: 10,
            },
            {
              cardStyleName: '1000元昆仑e享卡',
              faceValue: '990.0',
              cardNum: 1,
            },
          ],
        },
        {
          orderNo: '2401251539040007038473014',
          transactionPlaceName: '天津销售武清分公司高王路加油站',
          orderStatus: 2,
          realAmount: 990,
          createTime: '2024-8-10 15:30:34',
          orderTime: '2024-8-10 15:34:34',
          sourceChannelNo: 42,
          cardTypeNo: 5,
          giftCardItems: [
            {
              cardStyleName: '1000元昆仑e享卡',
              faceValue: '990.0',
              cardNum: 1,
            },
          ],
        },
        {
          orderNo: '2401251539040007038473014',
          transactionPlaceName: '天津销售武清分公司高王路加油站',
          orderStatus: 2,
          realAmount: 990,
          createTime: '2024-8-10 15:30:34',
          orderTime: '2024-8-10 15:34:34',
          sourceChannelNo: 42,
          cardTypeNo: 4,
          giftCardItems: [
            {
              cardStyleName: '1000元昆仑e享卡',
              faceValue: '990.0',
              cardNum: 1,
            },
          ],
        },
      ],
    },
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('user.business.getGiftCardOrderPage.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}

// 获取订单详情 // TODO 后台暂无此接口，暂时从列表取数据
export function getGiftCardOrderDetailApi(data, config) {
  const mockData = {
    success: true,
    data: {},
    message: '请求成功',
    errorCode: null,
  };
  return uni.$petro.http('user.business.getGiftCardOrderDetail.h5', data, {
    ...config,
    mockResponse: mockData,
  });
}
