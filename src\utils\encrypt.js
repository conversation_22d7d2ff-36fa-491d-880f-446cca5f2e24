// 引入 crypto-js 库
import CryptoJS from 'crypto-js';

// 将密钥转换为 WordArray
const key = CryptoJS.enc.Hex.parse('30313233343536373839414243444546'); // 将密钥转换为 WordArray

// 将 IV 从 Base64 字符串解析为 WordArray
const ivParsed = CryptoJS.enc.Hex.parse('30313233343536373839414243444546');

// 加密函数
export function encrypt(message) {
  // 获取当前时间戳并格式化
  const time = '0000000' + new Date().getTime();

  // 将消息和时间拼接后转换为 Utf8 编码的字节数组
  const scrs = CryptoJS.enc.Utf8.parse(message + time);

  // 使用 AES 算法进行加密
  const encrypted = CryptoJS.AES.encrypt(scrs, key, {
    // 初始化向量
    iv: ivParsed,
    // 加密模式
    mode: CryptoJS.mode.CBC,
    // 填充方式
    padding: CryptoJS.pad.Pkcs7,
  });

  // 将加密结果返回（通常使用 Base64 编码）
  return encrypted.ciphertext.toString(); // 转换为 Base64 字符串
}

// 解密函数
export function decrypt(encryptedStr, strLength = 20) {
  // 拿到字符串类型的密文需要先将其用Hex方法parse一下
  let encryptedHexStr = CryptoJS.enc.Hex.parse(encryptedStr);

  // 将密文转为Base64的字符串
  let encryptedBase64Str = CryptoJS.enc.Base64.stringify(encryptedHexStr);

  // 使用 AES 算法进行解密
  const encrypted = CryptoJS.AES.decrypt(encryptedBase64Str, key, {
    // 初始化向量
    iv: ivParsed,
    // 加密模式
    mode: CryptoJS.mode.CBC,
    // 填充方式
    padding: CryptoJS.pad.Pkcs7,
  });

  // 将解密结果转换字符串
  let str = encrypted.toString(CryptoJS.enc.Utf8);

  // 截取字符串
  return str.substring(0, str.length - strLength); // 转换为 Base64 字符串
}
