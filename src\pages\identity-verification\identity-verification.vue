<template>
  <div class="page-identity-verification">
    <u-navbar title="安全验证" :autoBack="true" :placeholder="true" :bgColor="'transparent'"> </u-navbar>

    <view class="black"></view>
    <view class="title" v-if="authStatus == 1">
      <view class="info">{{ mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') }}</view>
      <view>我们将发送短信验证码到这个账号</view>
    </view>
    <view class="title" v-else>
      <view>请识别本人人脸，保持正对搜集，确保光线充足</view>
      <view class="info">{{ name || '' }}&nbsp;{{ identityNo || '' }}</view>
    </view>
    <img class="icon-face" :src="require(`@/static/icon-review-${authStatus == 1 ? 'code' : 'face'}.png`)" />
    <view class="tips">
      您正在一台新设备登录，为了您的账号安全，{{
        authStatus == 1 ? '将对您进行安全校验。我们将严格保护您的信息安全。' : '将对您进行人脸识别安全校验。我们将严格保护您的信息安全。'
      }}
    </view>

    <button v-if="!isPermission" @click="onGetMetaInfo()">开启相机</button>
    <button v-else :class="{ disabled: disabled }" :disabled="disabled" @click="handleVerify()">开始验证</button>

    <!-- 滑块验证码弹窗 -->
    <div v-if="sliderVisable">
      <p-slider-verify
        :show="sliderVisable"
        :mobile="mobile"
        :messageType="messageType"
        :tempCode="tempCode"
        @success="onSliderVerifySuccess"
        @close="onSliderClose"
      />
    </div>

    <root-portal :enable="true">
      <zyzx-permission :show="permissionShow" :permissionName="'camera'" @cancle="permissionShow = false"></zyzx-permission>
    </root-portal>
  </div>
</template>

<script>
import { decrypt } from '@/utils';

export default {
  components: {},
  data() {
    return {
      sliderVisable: false, // 滑块验证是否显示
      mobile: '', // 手机号
      messageType: '4', // 1-用户注册 2-忘记密码 3-登录 4-换设备验证
      tempCode: '', // 临时验证码
      authStatus: 2, // 换设备校验状态:0-通过 1-短信验证 2-人脸验证
      location: {}, // 位置信息
      disabled: true, // 是否禁用按钮
      authenticationInfo: null, // 初始化实人认证返回信息
      name: '', // 姓名
      identityNo: '', // 身份证号
      saveInfo: {}, // 存储信息
      roleList: [], // 登录账号企业下角色列表
      role: {}, // 登录账号默认角色

      isPermission: true, // 是否有相机权限
      permissionShow: false, // 是否显示权限弹窗
    };
  },
  computed: {},
  async onLoad(query) {
    console.log('verify', query);
    this.messageType = query.messageType;
    this.mobile = query.mobile;
    this.tempCode = query.tempCode;
    this.authStatus = query.authStatus;
    if (query?.locationInfo) {
      this.location = JSON.parse(decodeURIComponent(query.locationInfo));
    }
    console.log('location', this.location);
    if (this.authStatus == 2) {
      this.onGetMetaInfo(true);
    } else {
      this.disabled = false;
    }
  },
  onShow() {
    if (!this.isPermission && this.authStatus == 2) {
      this.onGetMetaInfo();
    }
  },
  mounted() {},
  methods: {
    // 开始验证
    handleVerify() {
      if (this.authStatus == 1) {
        // 滑块验证-短信验证
        this.sliderVisable = true;
      } else {
        // 人脸验证
        this.onGetMetaInfo();
      }
    },
    // 滑块验证通过
    onSliderVerifySuccess() {
      this.sliderVisable = false;
      uni.showToast({ content: '短信验证码已发送' });
      this.toVerification();
    },
    // 滑块验证关闭
    onSliderClose(data) {
      this.sliderVisable = false;
      if (data?.isExpired) {
        setTimeout(() => {
          uni.$petro.route({
            type: 'back',
            delta: 1, // 默认是1，表示返回上一页，可不传delta
          });
        }, 500);
      }
    },
    // 跳转验证码页面
    toVerification() {
      setTimeout(() => {
        uni.$petro.route({
          url: '/pages/verification/verification',
          params: {
            mobile: this.mobile,
            messageType: this.messageType,
            tempCode: this.tempCode,
            locationInfo: encodeURIComponent(JSON.stringify(this.location)),
          },
          type: 'redirectTo',
        });
      }, 500);
    },
    // 权限-获取权限
    getPermissions(isInit = false) {
      return new Promise(async (resolve, reject) => {
        const res = await uni.$petro.preAuthPermissions({
          scopes: ['camera'],
          showExplainAlert: isInit, // 解释弹窗蒙层
          checkNotReq: !isInit, // 不请求权限
        });
        resolve(res);
      });
    },
    // 获取人脸识别元信息
    async onGetMetaInfo(isInit = false) {
      let permission = await this.getPermissions(isInit);

      this.isPermission = !!permission?.camera;
      if (!this.isPermission) {
        !isInit && (this.permissionShow = true);
        return;
      }

      const res = await uni.$petro.Bridge.zyzx.faceAuth({
        bizType: 'getMetaInfo',
        data: {},
      });

      this.initAuthentication(res.data.metaInfo, isInit);
    },
    // 初始化人脸认证
    async initAuthentication(metaInfo, isInit = false) {
      try {
        /**
         * 非登录态初始化实人认证接
         * metaInfo MetaInfo环境参数；由调用方通过前端插件或SDK获取后传入
         * tempCode 临时码
         * areaCode 认证地市编码 例如：320100
         */
        const res = await uni.$petro.http('user.memberInfo.noLoginInitRealPersonIdentify.h5', {
          metaInfo: metaInfo,
          tempCode: this.tempCode,
          areaCode: '',
        });
        console.log('res', res);

        if (!res?.success) return uni.showToast({ content: res.message || '初始化失败' });
        this.authenticationInfo = res.data;

        if (!!isInit) {
          let userInfo = decrypt(this.authenticationInfo?.userInfo);
          this.name = userInfo?.split('=')?.[0];
          this.identityNo = userInfo?.split('=')?.[1];
          this.disabled = false;
        } else {
          this.faceVerify();
        }
      } catch (e) {
        // 临时码已过期
        if (e.errorCode === 'B_B15_505777' || e.errorCode === 'B_B15_100001') {
          setTimeout(() => {
            uni.$petro.route({
              type: 'back',
              delta: 1, // 默认是1，表示返回上一页，可不传delta
            });
          }, 500);
          return;
        }
      }
    },
    // 人脸识别
    async faceVerify() {
      // 获取人脸识别凭证
      const res = await uni.$petro.Bridge.zyzx.faceAuth({
        bizType: 'verify',
        data: {
          certifyId: this.authenticationInfo.certifyId,
        },
      });

      if (!res?.success) return uni.showToast({ content: res.message || '人脸识别失败' });
      this.onLogin();
    },
    // 登录 -- 换设备登录 v420
    async onLogin() {
      try {
        /**
         * loginType 1—使用登录临时码及短信登录(app) 3—手机号+密码登录(app) 4—短信+换设备短信临时码(app) 5—换设备人脸临时码(app)
         * mobile 手机
         * tempCode 临时码 若loginType值为1、4、5，该字段必填。
         * messageCode 短信码 若loginType值为1、4，该字段必填。
         * password 登录密码。若loginType值为3，该字段必填。
         * loginLocation 登录地区
         * loginGps 登录gps坐标
         */
        const res = await uni.$petro.http(
          'user.login.h5',
          {
            loginType: '5',
            mobile: this.mobile,
            tempCode: this.tempCode,
            loginLocation: this.location?.address?.province + this.location?.address?.city,
            loginGps: this.location?.longitude ? `${this.location?.longitude},${this.location?.latitude}` : '',
          },
          { riskField: true },
        );
        console.log('res', res);
        if (!res?.success || !res?.data) return uni.showToast({ content: res.message || '登录失败' });
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = res.data || {};
        this.getUserRole();
      } catch (e) {
        // 临时码已过期
        if (e.errorCode === 'B_B15_505777' || e.errorCode === 'B_B15_100001') {
          setTimeout(() => {
            uni.$petro.route({
              type: 'back',
              delta: 1, // 默认是1，表示返回上一页，可不传delta
            });
          }, 500);
          return;
        }
      }
    },
    // 获取用户角色
    async getUserRole(reload = false) {
      try {
        const res = await uni.$petro.http('user.role.list.h5', {});
        console.log('res', res);

        // 获取角色列表--过滤掉企业管理员
        this.roleList = (res?.data || []).filter(item => item.code !== '1');

        // 判断是否有默认角色
        this.role = this.roleList.find(item => item.defaultFlag === 1) || {};

        if (reload) return;

        // 7-7正式供应商
        if (this.role?.code === '7-7') {
          setTimeout(() => {
            uni.$petro.switchTinyApp(this.role);
          }, 500);
          return;
        }

        await this.getUserEnterprise();
      } catch (e) {
        this.isLoading = false;
      }
    },
    // 获取获取已、未开通企业列表
    async getUserEnterprise() {
      try {
        const res = await uni.$petro.http('user.business.queryEnterpriseBusinessList.h5', {
          queryType: 5,
        });

        console.log('res', res);

        let managerList = (res?.data || []).filter(item => item.role === 2 && item.accountStatus !== 3); // 未注销企业列表
        let driverList = (res?.data || []).filter(item => item.role === 4 && item.accountStatus !== 3); // 未注销司机列表

        // 2-11礼品卡券管理员
        if (this.role?.code === '2-11') {
          let giftInfo = managerList.find(item => item.businessNo == this.role.businessNo && item.businessType === 11);
          if (giftInfo) {
            return [1, 2].includes(giftInfo.accountStatus) ? this.toGift() : this.toSetting();
          }
        }

        // 2-6非油直销业务联系人
        if (this.role?.code === '2-6') {
          let directsaleInfo = managerList.find(item => item.businessNo == this.role.businessNo && item.businessType === 6);
          if (directsaleInfo) {
            return [1, 2].includes(directsaleInfo.accountStatus) ? this.toDirectsale() : this.toSetting();
          }
        }

        let managerActivatedList = managerList.filter(item => [1, 2].includes(item.accountStatus) && item.businessType === 10); // 已激活/冻结车队业务联系人列表
        let driverActivatedList = driverList.filter(item => [1, 2].includes(item.accountStatus)); // 已激活/冻结司机列表

        // 已激活/冻结车队业务联系人
        let managerInfo = managerActivatedList.find(
          item => item.businessNo === this.role?.businessNo && item.enterpriseNo === this.saveInfo?.orgCode && this.role?.code === '2-10',
        );
        if (managerInfo) return this.toManager(managerInfo.businessNo);

        // 已激活/冻结司机
        let driverInfo = driverActivatedList.find(
          item => item.businessNo === this.role?.businessNo && item.enterpriseNo === this.saveInfo?.orgCode && this.role?.code === '4-10',
        );
        if (driverInfo) return this.toDriver(driverInfo.businessNo);

        if (managerActivatedList?.length) {
          // 已激活/冻结车队业务联系人--切换登录信息
          await this.switchCompanyAndRole(managerActivatedList[0].enterpriseNo, 2, 10, managerActivatedList[0].businessNo);
          this.toManager(managerActivatedList[0].businessNo);
        } else if (driverActivatedList?.length) {
          // 已激活/冻结司机--切换登录信息
          await this.switchCompanyAndRole(driverActivatedList[0].enterpriseNo, 4, 10, driverActivatedList[0].businessNo);
          this.toDriver();
        } else if (managerList.length || driverList.length) {
          // 有待激活企业
          this.toSetting();
        } else {
          // 无待激活企业--跳转产品页
          this.toProduct();
        }
      } catch (e) {
        this.isLoading = false;
        this.toProduct();
      } finally {
        setTimeout(() => {
          this.isLoading = false;
        }, 800);
      }
    },
    // 切换企业和角色
    async switchCompanyAndRole(enterpriseNo, roleType, roleBusinessType, businessNo) {
      try {
        const res = await uni.$petro.http('user.companySwitch.h5', {
          orgCode: enterpriseNo,
          type: 1,
          roleType,
          roleBusinessType,
          businessNo,
        });
        console.log('switchCompanyAndRole', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = res.data;
      } catch (e) {
        console.log(e);
      }
    },
    // 获取车队业务类型
    async getFleetType(businessNo) {
      try {
        const res = await uni.$petro.http('user.business.getFleetType.h5', {
          businessNo,
        });
        console.log('getFleetType', res);
        // 存储用户信息
        await uni.$petro.setTokenInfo(res.data);
        this.saveInfo = Object.assign(this.saveInfo, res.data);
      } catch (e) {
        console.log(e);
      }
    },
    // 跳转管理端
    async toManager(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-10' });
      }, 500);
    },
    // 跳转司机端
    async toDriver(businessNo) {
      await this.getFleetType(businessNo);
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '4-10' });
      }, 500);
    },
    // 跳转礼品卡
    toGift() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/gift-code/gift-code', type: 'reLaunch' });
      }, 500);
    },
    // 跳转非油直销
    toDirectsale() {
      setTimeout(() => {
        uni.$petro.switchTinyApp({ code: '2-6' });
      }, 500);
    },
    // 跳转激活页面
    toSetting() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/todo/todo', params: { isInit: 1 }, type: 'redirectTo' });
      }, 500);
    },
    // 跳转业务选择页面
    toProduct() {
      setTimeout(() => {
        uni.$petro.route({ url: '/pages/product/product', type: 'redirectTo' });
      }, 500);
    },
  },
};
</script>

<style scoped lang="scss">
.page-identity-verification {
  background: #ffffff;
  padding-bottom: 36rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

.black {
  height: 24rpx;
  background: #f0f1f5;
}

.title {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 28rpx;
  color: #333333;
  line-height: 32rpx;
  margin-top: 64rpx;
  text-align: center;

  & > view:nth-child(2) {
    margin-top: 20rpx;
  }
}

.info {
  font-weight: 500;
  font-size: 40rpx;
  color: #333333;
  line-height: 56rpx;
}

.icon-face {
  width: 320rpx;
  height: 320rpx;
  margin: 72rpx auto;
  display: block;
}

.tips {
  font-family: PingFang SC, PingFang SC;
  font-weight: 400;
  font-size: 24rpx;
  color: #999999;
  line-height: 28rpx;
  margin: 0 80rpx;
  text-align: center;
}

.tip-img {
  width: 100%;
  height: 150rpx;
  margin: 10rpx 0;
}

button {
  height: 98rpx;
  background: #fa1919;
  border: none;
  border-radius: 49rpx;
  text-align: center;
  line-height: 98rpx;
  font-family: PingFang SC, PingFang SC;
  font-weight: 500;
  font-size: 36rpx;
  color: #ffffff;
  margin: 48rpx 32rpx 0;

  &:active {
    opacity: 0.8;
  }
}

.disabled {
  background: #ccc;
}
</style>
